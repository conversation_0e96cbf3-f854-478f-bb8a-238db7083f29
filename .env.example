# 邮件处理机器人环境变量示例
# 复制此文件为 .env 并填入您的实际配置

# 邮箱配置
EMAIL_HOST=imap.gmail.com
EMAIL_PORT=993
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_USE_SSL=true
EMAIL_PROTOCOL=imap

# AI配置
AI_PROVIDER=openai
AI_API_KEY=your-api-key
AI_MODEL=gpt-3.5-turbo
AI_MAX_TOKENS=1000
AI_TEMPERATURE=0.7

# 调度器配置
SCHEDULER_CHECK_INTERVAL=300
SCHEDULER_MAX_EMAILS_PER_CHECK=50
SCHEDULER_ENABLE=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/mailer.log

# 智谱AI搜索配置（符合官方文档标准）
MCP_ENABLED=true
MCP_SERVER_URL=https://open.bigmodel.cn/api/mcp/web_search/sse
MCP_API_KEY=your_zhipu_api_key
MCP_TIMEOUT=30
MCP_MAX_RETRIES=3
MCP_RETRY_DELAY=1.0
MCP_CONNECTION_POOL_SIZE=5

# 智谱搜索特定配置
ZHIPU_SEARCH_PREFERRED_ENGINES=google,bing,baidu
ZHIPU_SEARCH_RELEVANCE_THRESHOLD=0.6
ZHIPU_SEARCH_CACHE_ENABLED=true
ZHIPU_SEARCH_CACHE_TTL=3600
ZHIPU_SEARCH_CACHE_MAX_ENTRIES=1000

# 备用搜索配置
FALLBACK_SEARCH_ENABLED=true
FALLBACK_SEARCH_ENGINE=duckduckgo
FALLBACK_SEARCH_TIMEOUT=15
FALLBACK_SEARCH_MAX_RESULTS=5

# 安全配置
USE_KEYRING=true
ENCRYPT_CONFIG=false
MCP_VERIFY_SSL=true
MCP_CONNECTION_ENCRYPTION=true
