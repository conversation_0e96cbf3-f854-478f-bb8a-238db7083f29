# Mailer AI Agent 系统架构重构计划

## 📋 项目概述

将现有的邮件处理机器人重构为通过邮件协同工作的通用AI Agent系统，参考 [suna](https://github.com/kortix-ai/suna) 项目的架构设计理念。

## 🎯 重构目标

### 核心功能
1. **邮件AI Agent**: 拥有独立邮箱的AI Agent，能够通过邮件进行智能交互和协同工作
2. **前端管理界面**: 支持在前端页面测试AI、教授新技能、配置MCP服务
3. **MCP集成**: 完善MCP（Model Context Protocol）客户端架构，支持动态加载和管理多种MCP服务
4. **沙箱环境**: 增强sandbox功能，支持浏览器、终端等工具的安全执行
5. **可扩展性**: 设计模块化架构，便于后续功能扩展和新技能集成

### 架构设计原则
- 采用微服务架构，各模块职责单一、低耦合
- 实现插件化设计，支持动态加载新功能模块
- 建立统一的配置管理和日志系统
- 设计清晰的API接口和数据流
- 保持向后兼容性，支持渐进式迁移

## 🏗️ 目标架构设计

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │ Agent Runtime   │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (Docker)      │
│                 │    │                 │    │                 │
│ - 邮件管理界面   │    │ - REST API      │    │ - 沙箱环境      │
│ - AI配置界面    │    │ - 邮件服务      │    │ - 浏览器自动化   │
│ - 技能管理界面   │    │ - Agent管理     │    │ - 工具集成      │
│ - 监控界面      │    │ - MCP集成       │    │ - 安全控制      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│   Database      │◄─────────────┘
                        │ (PostgreSQL +   │
                        │  Supabase)      │
                        │                 │
                        │ - 用户管理      │
                        │ - 邮件数据      │
                        │ - Agent配置     │
                        │ - 任务执行      │
                        │ - MCP服务       │
                        └─────────────────┘
```

### 新项目结构
```
mailer-ai-agent/
├── backend/                      # 后端API服务
│   ├── api/                     # REST API接口
│   │   ├── auth/                # 认证相关API
│   │   ├── email/               # 邮件管理API
│   │   ├── agents/              # Agent管理API
│   │   ├── tasks/               # 任务调度API
│   │   └── mcp/                 # MCP服务API
│   ├── core/                    # 核心业务逻辑
│   │   ├── email_service.py     # 邮件服务
│   │   ├── agent_manager.py     # Agent管理器
│   │   ├── task_scheduler.py    # 任务调度器
│   │   └── mcp_client.py        # MCP客户端
│   ├── models/                  # 数据模型
│   │   ├── user.py              # 用户模型
│   │   ├── email.py             # 邮件模型
│   │   ├── agent.py             # Agent模型
│   │   └── task.py              # 任务模型
│   ├── database/                # 数据库配置
│   │   ├── migrations/          # 数据库迁移
│   │   └── schemas/             # 数据库模式
│   ├── utils/                   # 工具函数
│   ├── config.py                # 配置管理
│   └── main.py                  # 应用入口
├── frontend/                     # 前端管理界面
│   ├── src/
│   │   ├── components/          # React组件
│   │   │   ├── email/           # 邮件相关组件
│   │   │   ├── agents/          # Agent相关组件
│   │   │   ├── tasks/           # 任务相关组件
│   │   │   └── common/          # 通用组件
│   │   ├── pages/               # 页面路由
│   │   │   ├── dashboard/       # 仪表板
│   │   │   ├── emails/          # 邮件管理
│   │   │   ├── agents/          # Agent管理
│   │   │   ├── skills/          # 技能管理
│   │   │   └── settings/        # 系统设置
│   │   ├── hooks/               # 自定义钩子
│   │   ├── services/            # API服务
│   │   ├── store/               # 状态管理
│   │   └── utils/               # 工具函数
│   ├── public/                  # 静态资源
│   ├── package.json             # 依赖配置
│   └── next.config.js           # Next.js配置
├── agent-runtime/                # Agent执行环境
│   ├── sandbox/                 # 沙箱环境
│   │   ├── browser/             # 浏览器自动化
│   │   ├── terminal/            # 终端执行
│   │   └── filesystem/          # 文件系统访问
│   ├── tools/                   # 工具集成
│   │   ├── web_search/          # 网络搜索
│   │   ├── file_manager/        # 文件管理
│   │   ├── code_executor/       # 代码执行
│   │   └── api_integrations/    # API集成
│   ├── security/                # 安全控制
│   │   ├── permissions.py       # 权限控制
│   │   ├── resource_limits.py   # 资源限制
│   │   └── audit_logger.py      # 审计日志
│   ├── Dockerfile               # Docker配置
│   └── requirements.txt         # Python依赖
├── database/                     # 数据库配置
│   ├── migrations/              # 数据库迁移文件
│   ├── seeds/                   # 初始数据
│   └── supabase/                # Supabase配置
├── docker/                       # Docker配置
│   ├── docker-compose.yml       # 服务编排
│   ├── backend.Dockerfile       # 后端镜像
│   ├── frontend.Dockerfile      # 前端镜像
│   └── agent.Dockerfile         # Agent镜像
├── docs/                         # 文档
│   ├── api/                     # API文档
│   ├── deployment/              # 部署文档
│   └── development/             # 开发文档
├── scripts/                      # 脚本工具
│   ├── setup.py                 # 安装脚本
│   ├── migrate.py               # 数据迁移脚本
│   └── deploy.py                # 部署脚本
├── tests/                        # 测试文件
│   ├── backend/                 # 后端测试
│   ├── frontend/                # 前端测试
│   └── integration/             # 集成测试
├── .env.example                  # 环境变量示例
├── docker-compose.yml            # 开发环境编排
├── README.md                     # 项目说明
└── MIGRATION_GUIDE.md            # 迁移指南
```

## 📊 现有代码分析

### 核心模块分析
1. **src/core/**: 包含配置管理、邮件客户端、AI分析器等核心功能
2. **src/processors/**: 包含各种邮件处理器，特别是ai_agent_components
3. **src/utils/**: 包含工具函数和辅助功能

### 可复用组件
- 邮件客户端逻辑 (EmailClient)
- AI分析器 (AIAnalyzer)
- MCP客户端实现 (ai_agent_components/components/mcp_client.py)
- PDF处理器 (pdf/)
- 配置管理系统 (ConfigManager)

### 需要重构的部分
- 单体架构 → 微服务架构
- 命令行界面 → Web界面
- 文件配置 → 数据库配置
- 简单调度 → 分布式任务队列
- 基础日志 → 结构化监控

## 🚀 实施计划

### 第一阶段：基础架构搭建 (2周)
- [ ] 创建新的项目结构
- [ ] 设置Docker开发环境
- [ ] 配置数据库和基础API
- [ ] 创建前端框架

### 第二阶段：核心功能迁移 (2周)
- [ ] 迁移邮件处理逻辑
- [ ] 重构AI Agent系统
- [ ] 集成MCP客户端
- [ ] 实现基础前端界面

### 第三阶段：高级功能开发 (2周)
- [ ] 开发沙箱执行环境
- [ ] 实现技能管理系统
- [ ] 添加监控和日志
- [ ] 完善安全机制

### 第四阶段：测试和优化 (2周)
- [ ] 编写测试用例
- [ ] 性能优化
- [ ] 文档编写
- [ ] 部署配置

## 📝 下一步行动

1. **确认重构方案**: 请确认以上重构计划是否符合您的期望
2. **开始实施**: 从第一阶段开始，逐步创建新的项目结构
3. **保持兼容**: 在重构过程中保持现有功能的可用性
4. **渐进迁移**: 逐步将现有代码迁移到新架构中

请确认是否开始实施这个重构计划？
