# 真实网络搜索集成指南

## 📋 概述

AI Reply Processor 现已集成真实的网络搜索功能，支持三种搜索模式：

1. **MCP搜索** - 使用 zhipu-web-search-sse MCP 协议
2. **备用搜索** - 使用 DuckDuckGo 等搜索引擎
3. **模拟搜索** - 用于测试和备用的模拟数据

## 🚀 功能特性

### ✅ 已实现功能

- **多搜索源支持**: MCP、备用搜索引擎、模拟搜索
- **自动降级**: MCP失败时自动切换到备用搜索
- **搜索质量评估**: 智能相关性评分和结果排序
- **多种搜索类型**: 通用、技术、学术、新闻、产品搜索
- **配置灵活**: 支持详细的搜索参数配置
- **错误处理**: 完善的超时和重试机制

### 🔄 搜索流程

```
搜索请求 → 模式选择
            ├── MCP可用 → zhipu-web-search-sse MCP
            ├── MCP失败 → 备用搜索 (DuckDuckGo)
            └── 全部失败 → 模拟搜索 (测试数据)
```

## ⚙️ 配置说明

### 基础配置

```yaml
processors:
  ai_reply_processor:
    search:
      max_results: 5              # 最大搜索结果数
      timeout: 30                 # 搜索超时时间(秒)
      language: "zh"              # 搜索语言
      region: "CN"                # 搜索地区
```

### MCP配置 (zhipu-web-search-sse)

```yaml
search:
  mcp:
    enabled: true                 # 启用MCP搜索
    server_url: "ws://localhost:8080"  # MCP服务器地址
    api_key: "your_zhipu_api_key"      # API密钥
    timeout: 30                   # 连接超时时间
    max_retries: 3                # 最大重试次数
```

### 备用搜索配置

```yaml
search:
  fallback:
    enabled: true                 # 启用备用搜索
    timeout: 30                   # 搜索超时时间
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
```

### 搜索质量配置

```yaml
search:
  quality_weights:
    title_relevance: 0.3          # 标题相关性权重
    snippet_relevance: 0.4        # 摘要相关性权重
    source_authority: 0.2         # 来源权威性权重
    freshness: 0.1                # 时效性权重
```

## 🔧 安装和设置

### 1. 安装依赖

```bash
pip install httpx websockets aiohttp beautifulsoup4 mcp
```

或者更新 requirements.txt 后运行：

```bash
pip install -r requirements.txt
```

### 2. 配置 zhipu-web-search-sse MCP

#### 方式一：使用官方MCP服务器

1. 获取智谱AI API密钥
2. 启动 zhipu-web-search-sse MCP 服务器
3. 配置连接参数

#### 方式二：自建MCP服务器

```bash
# 克隆MCP服务器代码
git clone https://github.com/your-repo/zhipu-web-search-sse
cd zhipu-web-search-sse

# 安装依赖
npm install

# 配置环境变量
export ZHIPU_API_KEY="your_api_key"

# 启动服务器
npm start
```

### 3. 测试搜索功能

```bash
python test_real_search.py
```

## 📊 使用示例

### 基本搜索

```python
from src.processors.ai_agent_components.components.search_handler import SearchHandler
from src.processors.ai_agent_components.models.search_models import SearchType

# 创建搜索处理器
config = {
    "max_results": 5,
    "timeout": 30,
    "mcp": {
        "enabled": True,
        "server_url": "ws://localhost:8080",
        "api_key": "your_api_key"
    }
}

search_handler = SearchHandler(config)

# 执行搜索
search_context = search_handler.search("人工智能发展趋势", SearchType.GENERAL)

# 处理结果
for result in search_context.results:
    print(f"标题: {result.title}")
    print(f"URL: {result.url}")
    print(f"摘要: {result.snippet}")
    print(f"相关性: {result.relevance_score}")
```

### 多查询搜索

```python
# 执行多个搜索查询
queries = ["机器学习", "深度学习", "神经网络"]
search_contexts = search_handler.search_multiple_queries(queries, SearchType.TECHNICAL)

for context in search_contexts:
    print(f"查询: {context.request.query}")
    print(f"结果数: {context.total_results}")
```

### 搜索结果分析

```python
# 获取搜索摘要
summary = search_context.get_summary()
print(f"搜索摘要:\n{summary}")

# 获取前3个最相关结果
top_results = search_context.get_top_results(3)
for result in top_results:
    print(f"- {result.title} (评分: {result.relevance_score:.2f})")
```

## 🔍 搜索类型说明

| 搜索类型 | 说明 | 适用场景 |
|---------|------|----------|
| GENERAL | 通用搜索 | 一般性问题和信息查询 |
| TECHNICAL | 技术搜索 | 编程、技术文档、教程 |
| ACADEMIC | 学术搜索 | 学术论文、研究报告 |
| NEWS | 新闻搜索 | 最新新闻、时事信息 |
| PRODUCT | 产品搜索 | 产品评测、购买建议 |

## 🛠️ 故障排除

### 常见问题

#### 1. MCP连接失败

**问题**: `连接MCP服务器失败`

**解决方案**:
- 检查MCP服务器是否运行
- 验证服务器地址和端口
- 确认API密钥正确
- 检查网络连接

#### 2. 备用搜索失败

**问题**: `备用搜索失败`

**解决方案**:
- 检查网络连接
- 验证User-Agent设置
- 增加超时时间
- 检查防火墙设置

#### 3. 搜索结果质量差

**问题**: 搜索结果不相关

**解决方案**:
- 调整搜索关键词
- 修改质量权重配置
- 使用更具体的搜索类型
- 增加搜索结果数量

### 调试技巧

#### 启用详细日志

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

#### 检查搜索模式

```python
search_handler = SearchHandler(config)
print(f"当前搜索模式: {search_handler.search_mode}")
```

#### 测试连接

```python
# 测试MCP连接
if search_handler.mcp_client:
    try:
        # 尝试简单搜索
        results = search_handler.mcp_client.search_web_sync("test")
        print("MCP连接正常")
    except Exception as e:
        print(f"MCP连接失败: {e}")
```

## 📈 性能优化

### 搜索缓存

```python
# 在配置中启用缓存
config = {
    "cache": {
        "enabled": True,
        "ttl": 3600,  # 缓存1小时
        "max_size": 1000
    }
}
```

### 并发搜索

```python
# 启用并发搜索（谨慎使用）
config = {
    "concurrent": {
        "enabled": True,
        "max_workers": 3
    }
}
```

### 超时优化

```python
# 根据网络情况调整超时
config = {
    "timeout": 15,  # 较快的网络
    "mcp": {
        "timeout": 20,
        "max_retries": 2
    },
    "fallback": {
        "timeout": 10
    }
}
```

## 🔒 安全考虑

### API密钥保护

- 使用环境变量存储API密钥
- 不要在代码中硬编码密钥
- 定期轮换API密钥

### 请求限制

- 设置合理的请求频率限制
- 使用缓存减少重复请求
- 监控API使用量

### 数据隐私

- 不要搜索敏感信息
- 记录搜索日志时注意隐私
- 遵守相关法律法规

## 📝 总结

新的搜索功能为AI Reply Processor提供了强大的信息获取能力：

1. **多层次备用**: MCP → 备用搜索 → 模拟搜索
2. **智能评估**: 相关性评分和质量排序
3. **灵活配置**: 支持各种搜索参数调整
4. **错误恢复**: 完善的错误处理和重试机制
5. **性能优化**: 支持缓存和并发搜索

通过合理配置和使用，可以显著提升AI回复的信息准确性和时效性。
