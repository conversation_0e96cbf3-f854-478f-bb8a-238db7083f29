# 邮件线程上下文管理系统设计文档

## 1. 系统架构
```mermaid
graph LR
    邮件系统 --> ThreadContextProcessor
    ThreadContextProcessor --> ThreadDB[(线程数据库)]
    ThreadContextProcessor --> ContextEngine[上下文引擎]
    ContextEngine --> FullHistoryBuilder
    ContextEngine --> SummaryBuilder
    ContextEngine --> KeyPointsBuilder
    ThreadContextProcessor --> AI处理器管道
    AI处理器管道 --> ClarificationProcessor
    AI处理器管道 --> ResponseGenerator
```

## 2. 数据库规范
### 2.1 ER图
```mermaid
erDiagram
    THREAD ||--o{ MESSAGE : contains
    THREAD ||--o{ CLARIFICATION : has
    THREAD {
        string thread_id PK
        string subject
        datetime created_at
        string status
    }
    MESSAGE {
        string message_id PK
        string thread_id FK
        text content
        json metadata
        datetime timestamp
    }
    CLARIFICATION {
        string session_id PK
        string thread_id FK
        json missing_info
        datetime expires_at
    }
```

### 2.2 SQL定义
```sql
-- threads表
CREATE TABLE threads (
    thread_id TEXT PRIMARY KEY,
    subject TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT CHECK(status IN ('active','resolved','expired'))
);

-- messages表
CREATE TABLE messages (
    message_id TEXT PRIMARY KEY,
    thread_id TEXT REFERENCES threads(thread_id),
    content TEXT NOT NULL,
    metadata JSON,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 3. 核心处理流程
```mermaid
sequenceDiagram
    participant 邮件系统
    participant ThreadContextProcessor
    participant ThreadDB
    participant ContextEngine
    participant AI处理器
    
    邮件系统->>ThreadContextProcessor: 新邮件(含Thread-ID)
    ThreadContextProcessor->>ThreadDB: 查询thread_id
    alt 新线程
        ThreadDB-->>ThreadContextProcessor: 未找到记录
        ThreadContextProcessor->>ThreadDB: 创建新线程
        ThreadContextProcessor->>ContextEngine: 构建空上下文
    else 现有线程
        ThreadDB-->>ThreadContextProcessor: 返回线程数据
        ThreadContextProcessor->>ContextEngine: 构建历史上下文
    end
    ThreadContextProcessor->>AI处理器: 传递邮件+上下文
    AI处理器->>邮件系统: 生成响应
```

## 4. RAG集成管道
```mermaid
flowchart TB
    A[新邮件] --> B[元数据提取]
    B --> C[文本向量化]
    C --> D[FAISS存储]
    D --> E[向量索引]
    E --> F[RAG查询接口]
```

## 5. 配置规范
```yaml
context_management:
  strategy_priority: [summary, key_points]
  max_tokens: 2000
  vector_storage:
    adapter: faiss
    index_path: data/email_index.faiss
    backup_interval: 3600  # 每小时备份
```

## 6. 实施路线
| 阶段 | 周期 | 交付物 | 测试要求 |
|------|------|--------|----------|
| 核心架构 | 2周 | 线程管理+上下文引擎 | 单元测试覆盖率≥80% |
| RAG管道 | 1周 | 向量存储+查询接口 | 性能测试：QPS≥100 |
| 系统集成 | 1周 | 配置系统+监控面板 | 集成测试全流程 |