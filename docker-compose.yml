# Mailer AI Agent 系统 Docker Compose 配置
# 用于开发环境的完整服务编排

version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: mailer-postgres
    environment:
      POSTGRES_DB: mailer_ai_agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - mailer-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d mailer_ai_agent"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存和任务队列
  redis:
    image: redis:7-alpine
    container_name: mailer-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - mailer-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: mailer-backend
    ports:
      - "8000:8000"
    environment:
      # 数据库配置
      - DATABASE_URL=********************************************/mailer_ai_agent
      - REDIS_URL=redis://redis:6379/0
      
      # 应用配置
      - ENVIRONMENT=development
      - DEBUG=true
      - SECRET_KEY=dev-secret-key-change-in-production
      
      # AI 服务配置
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - ZHIPU_API_KEY=${ZHIPU_API_KEY:-}
      
      # MCP 配置
      - MCP_ENABLED=true
      - MCP_SERVER_URL=https://open.bigmodel.cn/api/mcp/web_search/sse
      
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FILE=/app/logs/backend.log
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mailer-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: mailer-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
      - NEXT_PUBLIC_APP_NAME=Mailer AI Agent
      - NEXT_PUBLIC_APP_VERSION=1.0.0
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - mailer-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Agent 运行时服务
  agent-runtime:
    build:
      context: ./agent-runtime
      dockerfile: Dockerfile
      target: development
    container_name: mailer-agent-runtime
    ports:
      - "8001:8001"
    environment:
      - BACKEND_URL=http://backend:8000
      - REDIS_URL=redis://redis:6379/1
      - SANDBOX_ENABLED=true
      - BROWSER_ENABLED=true
      - MAX_CONCURRENT_AGENTS=5
      - AGENT_TIMEOUT=300
    volumes:
      - ./agent-runtime:/app
      - /var/run/docker.sock:/var/run/docker.sock
      - agent_workspace:/app/workspace
    depends_on:
      - backend
      - redis
    networks:
      - mailer-network
    restart: unless-stopped
    privileged: true  # 需要访问Docker socket
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery 任务队列工作者
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: mailer-celery-worker
    command: celery -A core.celery_app worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=********************************************/mailer_ai_agent
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mailer-network
    restart: unless-stopped

  # Celery 任务调度器
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: mailer-celery-beat
    command: celery -A core.celery_app beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DATABASE_URL=********************************************/mailer_ai_agent
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mailer-network
    restart: unless-stopped

  # Flower 任务监控 (可选)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: mailer-flower
    command: celery -A core.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    networks:
      - mailer-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Nginx 反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: mailer-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - mailer-network
    restart: unless-stopped
    profiles:
      - production

# 网络配置
networks:
  mailer-network:
    driver: bridge
    name: mailer-ai-agent-network

# 数据卷配置
volumes:
  postgres_data:
    name: mailer-postgres-data
  redis_data:
    name: mailer-redis-data
  agent_workspace:
    name: mailer-agent-workspace

# 开发环境快速启动命令示例:
# docker-compose up -d postgres redis
# docker-compose up backend frontend
#
# 生产环境启动命令示例:
# docker-compose --profile production up -d
#
# 监控环境启动命令示例:
# docker-compose --profile monitoring up -d flower
