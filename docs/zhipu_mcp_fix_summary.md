# 智谱AI MCP客户端修复总结

## 问题分析

通过对比智谱AI官方文档(https://mcp.so/server/zhipu-web-search/BigModel)和当前代码实现，发现了以下关键问题：

### 1. URL格式不符合官方标准
- **当前实现**: 使用WebSocket连接 `ws://localhost:8080`
- **官方要求**: 使用HTTPS SSE连接 `https://open.bigmodel.cn/api/mcp/web_search/sse?Authorization={your_api_key}`

### 2. 连接方式错误
- **当前实现**: 基于WebSocket的MCP协议连接
- **官方要求**: 基于Server-Sent Events (SSE) 的HTTP连接

### 3. API密钥传递方式不正确
- **当前实现**: 通过WebSocket headers传递 `Authorization: Bearer {api_key}`
- **官方要求**: 通过URL参数传递 `Authorization={your_api_key}`

### 4. 协议实现不匹配
- **当前实现**: 完整的MCP WebSocket客户端实现
- **官方要求**: zhipu-web-search-sse是一个特定的SSE服务，不是标准MCP服务器

## 修复方案

### 1. 重构核心类结构

#### 新增类和枚举
- `ZhipuConnectionState`: 智谱搜索连接状态枚举
- `ZhipuSearchRequest`: 智谱搜索请求数据结构
- `ZhipuSearchResponse`: 智谱搜索响应数据结构
- `ZhipuConnectionPool`: 智谱AI连接池管理器
- `ZhipuSearchClient`: 智谱AI Web搜索客户端主类

#### 向后兼容性
```python
# 向后兼容性别名
MCPClient = ZhipuSearchClient
MCPConnectionState = ZhipuConnectionState
MCPRequest = ZhipuSearchRequest
MCPResponse = ZhipuSearchResponse
MCPConnectionPool = ZhipuConnectionPool
```

### 2. 更新连接实现

#### 从WebSocket改为HTTP/SSE
```python
# 旧实现 (WebSocket)
self.websocket = await websockets.connect(
    self.server_url,
    additional_headers=headers,
    open_timeout=self.timeout
)

# 新实现 (HTTP客户端)
self.http_client = httpx.AsyncClient(
    timeout=httpx.Timeout(self.timeout),
    headers=headers,
    follow_redirects=True
)
```

#### 官方API URL格式
```python
# 构建请求URL（按照官方文档格式）
url = f"{self.base_url}?Authorization={self.api_key}"
```

### 3. 重写搜索请求方法

#### SSE响应处理
```python
# 发送POST请求到SSE端点
response = await self.http_client.post(
    url,
    json=request_data,
    headers={
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
    }
)

# 处理SSE响应
results = []
async for line in response.aiter_lines():
    if line.startswith('data: '):
        try:
            data = json.loads(line[6:])  # 移除 'data: ' 前缀
            if 'results' in data:
                results.extend(data['results'])
            elif 'result' in data:
                results.append(data['result'])
        except json.JSONDecodeError:
            continue
```

### 4. 更新配置文件

#### config_mcp_example.yaml
```yaml
# 智谱AI搜索客户端配置（符合官方文档标准）
mcp:
  enabled: true
  server_url: "https://open.bigmodel.cn/api/mcp/web_search/sse"
  api_key: "your_zhipu_api_key"
  timeout: 30
  max_retries: 3
  retry_delay: 1.0
  connection_pool_size: 5
```

#### .env.example
```bash
# 智谱AI搜索配置（符合官方文档标准）
MCP_ENABLED=true
MCP_SERVER_URL=https://open.bigmodel.cn/api/mcp/web_search/sse
MCP_API_KEY=your_zhipu_api_key
```

### 5. 更新依赖

#### requirements.txt
```
# 网络搜索和智谱AI SSE支持
httpx>=0.25.0
sseclient-py>=1.8.0
aiohttp>=3.9.0
beautifulsoup4>=4.12.0
```

移除了不再需要的：
- `websockets>=12.0`
- `mcp>=1.0.0`

## 主要改进

### 1. 符合官方标准
- 使用智谱AI官方SSE API端点
- 正确的API密钥传递方式
- 标准的HTTP/SSE协议实现

### 2. 保持向后兼容性
- 所有原有的类名和接口保持可用
- 现有代码无需修改即可使用新实现
- 配置格式保持兼容

### 3. 改进的错误处理
- 针对HTTP状态码的专门错误处理
- SSE连接的超时和重试机制
- 更详细的日志记录

### 4. 性能优化
- 移除不必要的心跳检测机制
- 更高效的HTTP连接池管理
- 优化的SSE数据解析

## 使用方法

### 1. 安装新依赖
```bash
pip install httpx sseclient-py
```

### 2. 更新配置
在配置文件中设置正确的智谱AI API密钥和端点：
```yaml
ai:
  mcp:
    enabled: true
    server_url: "https://open.bigmodel.cn/api/mcp/web_search/sse"
    api_key: "your_zhipu_api_key"
```

### 3. 代码使用
现有代码无需修改，继续使用原有接口：
```python
from src.processors.ai_agent_components.components.mcp_client import MCPClient

client = MCPClient(config)
results = await client.search_web("搜索查询")
```

## 测试建议

1. **连接测试**: 验证能否成功连接到智谱AI SSE端点
2. **搜索测试**: 测试各种搜索查询的响应格式
3. **错误处理测试**: 测试网络错误、API错误等异常情况
4. **性能测试**: 对比新旧实现的响应时间和资源使用
5. **兼容性测试**: 确保现有代码无需修改即可正常工作

## 注意事项

1. **API密钥**: 必须使用有效的智谱AI API密钥
2. **网络环境**: 确保能够访问智谱AI的官方API端点
3. **依赖更新**: 及时更新相关依赖包到最新版本
4. **监控日志**: 关注搜索请求的成功率和响应时间
