# MCP客户端集成指南

## 概述

本文档介绍如何在mailer项目中配置和使用MCP (Model Context Protocol) 客户端，特别是与zhipu-web-search-sse MCP服务器的集成。

## 功能特性

### 核心功能
- **连接池管理**: 支持多连接并发处理
- **自动重连**: 连接断开时自动重连
- **心跳检测**: 定期检测连接状态
- **错误处理**: 完善的错误处理和回退机制
- **结果标准化**: 统一不同MCP服务器的响应格式

### zhipu-web-search-sse 集成
- **智能搜索**: 支持多种搜索引擎
- **结果过滤**: 可配置的内容过滤规则
- **质量控制**: 相关性和权威性评估
- **缓存机制**: 搜索结果缓存优化

## 配置说明

### 基本配置

```yaml
ai:
  mcp:
    enabled: true                           # 启用MCP功能
    server_url: "ws://localhost:8080"       # MCP服务器地址
    api_key: "your_mcp_api_key"            # API密钥（可选）
    timeout: 30                            # 连接超时时间
    max_retries: 3                         # 最大重试次数
    retry_delay: 1.0                       # 重试延迟
    connection_pool_size: 5                # 连接池大小
    heartbeat_interval: 30                 # 心跳间隔
```

### zhipu-web-search-sse 特定配置

```yaml
ai:
  mcp:
    zhipu_search:
      preferred_engines:                   # 偏好搜索引擎
        - "google"
        - "bing"
        - "baidu"
      
      content_filter:                      # 内容过滤
        min_content_length: 50
        exclude_domains:
          - "spam-site.com"
        include_file_types:
          - "html"
          - "pdf"
      
      quality_settings:                    # 质量设置
        relevance_threshold: 0.6
        freshness_weight: 0.2
        authority_weight: 0.3
      
      cache:                              # 缓存设置
        enabled: true
        ttl: 3600
        max_entries: 1000
```

### 备用搜索配置

```yaml
ai:
  mcp:
    fallback:
      enabled: true                        # 启用备用搜索
      search_engine: "duckduckgo"         # 备用搜索引擎
      timeout: 15                         # 超时时间
      max_results: 5                      # 最大结果数
```

## 使用方法

### 1. 基本搜索

```python
from src.processors.ai_agent_components.components.search_handler import SearchHandler
from src.processors.ai_agent_components.models.search_models import SearchType

# 初始化搜索处理器
config = {
    'mcp': {
        'enabled': True,
        'server_url': 'ws://localhost:8080'
    }
}
handler = SearchHandler(config)

# 执行搜索
context = handler.search("人工智能发展趋势", SearchType.TECHNICAL)

# 获取结果
for result in context.results:
    print(f"标题: {result.title}")
    print(f"URL: {result.url}")
    print(f"摘要: {result.snippet}")
    print(f"相关性: {result.relevance_score}")
    print("---")
```

### 2. 异步搜索

```python
import asyncio
from src.processors.ai_agent_components.components.mcp_client import MCPClient

async def async_search():
    config = {
        'server_url': 'ws://localhost:8080',
        'api_key': 'your_api_key'
    }
    
    async with MCPClient(config) as client:
        results = await client.search_web(
            query="机器学习算法",
            max_results=10,
            language="zh",
            region="CN"
        )
        
        for result in results:
            print(f"找到结果: {result['title']}")

# 运行异步搜索
asyncio.run(async_search())
```

### 3. 连接状态检查

```python
# 检查搜索统计信息
stats = handler.get_search_statistics()
print(f"搜索模式: {stats['search_mode']}")
print(f"MCP可用: {stats['mcp_available']}")
print(f"连接状态: {stats['mcp_connection_status']}")

# 测试MCP连接
if handler.test_mcp_connection():
    print("MCP连接正常")
else:
    print("MCP连接失败")
```

## 环境变量配置

可以通过环境变量覆盖配置文件中的设置：

```bash
# MCP基本配置
export MCP_ENABLED=true
export MCP_SERVER_URL=ws://your-mcp-server:8080
export MCP_API_KEY=your_mcp_api_key
export MCP_TIMEOUT=30
export MCP_MAX_RETRIES=3

# 连接池配置
export MCP_CONNECTION_POOL_SIZE=5
export MCP_HEARTBEAT_INTERVAL=30
```

## 错误处理

### 连接错误
- 自动重连机制
- 连接池故障转移
- 备用搜索降级

### 搜索错误
- 请求重试
- 结果验证
- 错误日志记录

### 示例错误处理

```python
try:
    context = handler.search("查询内容")
    if context.error_message:
        print(f"搜索出错: {context.error_message}")
    else:
        print(f"找到 {len(context.results)} 个结果")
except Exception as e:
    print(f"搜索异常: {e}")
```

## 性能优化

### 连接池优化
- 合理设置连接池大小
- 启用连接复用
- 配置心跳检测

### 搜索优化
- 启用结果缓存
- 设置合适的超时时间
- 使用批量搜索

### 示例配置

```yaml
ai:
  mcp:
    connection_pool_size: 10              # 增加连接池大小
    heartbeat_interval: 15                # 缩短心跳间隔
    zhipu_search:
      cache:
        enabled: true                     # 启用缓存
        ttl: 7200                        # 延长缓存时间
        max_entries: 2000                # 增加缓存容量
```

## 监控和调试

### 日志配置

```yaml
log:
  level: "DEBUG"                          # 启用调试日志
  file_path: "logs/mcp_client.log"
```

### 监控指标

```python
# 获取详细统计信息
stats = handler.get_search_statistics()
print(f"搜索模式: {stats['search_mode']}")
print(f"MCP状态: {stats['mcp_connection_status']}")
```

### 调试模式

```yaml
development:
  debug_mode: true                        # 启用调试模式
  log_mcp_messages: true                  # 记录MCP消息
  mock_mcp_responses: false               # 禁用模拟响应
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查MCP服务器地址和端口
   - 验证API密钥
   - 确认网络连接

2. **搜索无结果**
   - 检查搜索参数
   - 验证MCP服务器状态
   - 查看错误日志

3. **性能问题**
   - 调整连接池大小
   - 优化搜索参数
   - 启用缓存机制

### 诊断命令

```python
# 测试MCP连接
handler.test_mcp_connection()

# 查看搜索统计
stats = handler.get_search_statistics()

# 执行诊断搜索
context = handler.search("test", max_results=1)
```

## 最佳实践

1. **配置管理**
   - 使用环境变量存储敏感信息
   - 分环境配置不同的MCP服务器
   - 定期更新API密钥

2. **错误处理**
   - 实现完善的错误处理逻辑
   - 配置合适的重试策略
   - 启用备用搜索机制

3. **性能优化**
   - 合理设置连接池大小
   - 启用搜索结果缓存
   - 监控搜索性能指标

4. **安全考虑**
   - 验证SSL证书
   - 限制允许的服务器主机
   - 启用连接加密

## 更新日志

### v1.0.0
- 初始版本发布
- 支持zhipu-web-search-sse集成
- 实现连接池和自动重连
- 添加备用搜索机制

### 后续计划
- 支持更多MCP服务器
- 增强缓存机制
- 添加性能监控
- 优化错误处理
