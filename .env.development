# 开发环境配置
ENVIRONMENT=development
DEBUG=true

# 数据库配置
DATABASE_URL=postgresql://mailer_user:mailer_pass@localhost:5432/mailer_dev
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=mailer_dev
DATABASE_USER=mailer_user
DATABASE_PASSWORD=mailer_pass

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379

# 应用配置
SECRET_KEY=dev-secret-key-change-in-production
JWT_SECRET_KEY=dev-jwt-secret-key

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:8000

# 日志配置
LOG_LEVEL=DEBUG
LOG_FILE=./logs/mailer_dev.log

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
