#!/usr/bin/env python3
"""
智谱AI搜索客户端测试脚本
用于验证修复后的MCP客户端能否正常连接和工作
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.core.config_manager import ConfigManager
from src.processors.ai_agent_components.components.mcp_client import ZhipuSearchClient, MCPClient

async def test_zhipu_connection():
    """测试智谱AI连接"""
    logger.info("开始测试智谱AI搜索客户端连接...")
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()

        # 获取MCP配置
        mcp_config = config.ai.mcp.dict()
        logger.info(f"MCP配置已加载，启用状态: {mcp_config.get('enabled', False)}")

        # 创建智谱搜索客户端
        client = ZhipuSearchClient(mcp_config)
        
        # 测试连接
        logger.info("尝试连接到智谱AI服务...")
        connected = await client.connect()
        
        if connected:
            logger.success("✅ 智谱AI连接成功!")
            return client
        else:
            logger.error("❌ 智谱AI连接失败!")
            return None
            
    except Exception as e:
        logger.error(f"❌ 连接测试失败: {e}")
        return None

async def test_search_functionality(client):
    """测试搜索功能"""
    logger.info("开始测试搜索功能...")
    
    test_queries = [
        "人工智能最新发展",
        "Python编程技巧",
        "2024年科技趋势"
    ]
    
    for query in test_queries:
        try:
            logger.info(f"搜索查询: {query}")
            
            # 执行搜索
            results = await client.search_web(query, max_results=3)
            
            if results:
                logger.success(f"✅ 搜索成功，找到 {len(results)} 个结果:")
                for i, result in enumerate(results, 1):
                    logger.info(f"  {i}. {result.get('title', 'N/A')}")
                    logger.info(f"     URL: {result.get('url', 'N/A')}")
                    logger.info(f"     摘要: {result.get('snippet', 'N/A')[:100]}...")
                    logger.info("")
            else:
                logger.warning(f"⚠️ 搜索 '{query}' 未返回结果")
                
        except Exception as e:
            logger.error(f"❌ 搜索 '{query}' 失败: {e}")
        
        # 等待一下避免请求过快
        await asyncio.sleep(2)

async def test_backward_compatibility():
    """测试向后兼容性"""
    logger.info("开始测试向后兼容性...")
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        mcp_config = config.ai.mcp.dict()
        
        # 使用原有的MCPClient别名
        client = MCPClient(mcp_config)
        
        # 测试连接
        connected = await client.connect()
        
        if connected:
            logger.success("✅ 向后兼容性测试通过!")
            
            # 测试一个简单搜索
            results = await client.search_web("测试查询", max_results=1)
            if results:
                logger.success("✅ 向后兼容搜索功能正常!")
            else:
                logger.warning("⚠️ 向后兼容搜索未返回结果")
                
            await client.disconnect()
        else:
            logger.error("❌ 向后兼容性测试失败!")
            
    except Exception as e:
        logger.error(f"❌ 向后兼容性测试异常: {e}")

async def test_error_handling():
    """测试错误处理"""
    logger.info("开始测试错误处理...")
    
    try:
        # 测试无效API密钥
        invalid_config = {
            'server_url': 'https://open.bigmodel.cn/api/mcp/web_search/sse',
            'api_key': 'invalid_key',
            'timeout': 10,
            'max_retries': 1
        }
        
        client = ZhipuSearchClient(invalid_config)
        
        try:
            results = await client.search_web("测试", max_results=1)
            if not results:
                logger.success("✅ 无效API密钥错误处理正常")
            else:
                logger.warning("⚠️ 无效API密钥应该返回空结果")
        except Exception as e:
            logger.success(f"✅ 无效API密钥正确抛出异常: {e}")
            
    except Exception as e:
        logger.error(f"❌ 错误处理测试异常: {e}")

async def main():
    """主测试函数"""
    logger.info("🚀 开始智谱AI搜索客户端全面测试")
    logger.info("=" * 60)
    
    # 1. 测试连接
    client = await test_zhipu_connection()
    
    if client:
        # 2. 测试搜索功能
        await test_search_functionality(client)
        
        # 断开连接
        await client.disconnect()
        logger.info("智谱AI客户端已断开连接")
    
    logger.info("=" * 60)
    
    # 3. 测试向后兼容性
    await test_backward_compatibility()
    
    logger.info("=" * 60)
    
    # 4. 测试错误处理
    await test_error_handling()
    
    logger.info("=" * 60)
    logger.info("🎉 智谱AI搜索客户端测试完成!")

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    asyncio.run(main())
