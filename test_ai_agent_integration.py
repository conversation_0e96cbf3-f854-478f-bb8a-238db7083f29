#!/usr/bin/env python3
"""
测试AI Agent系统与智谱AI搜索客户端的集成
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.core.config_manager import ConfigManager
from src.processors.ai_agent_components.components.search_handler import SearchHandler

async def test_search_handler_integration():
    """测试搜索处理器与智谱AI的集成"""
    logger.info("开始测试AI Agent搜索处理器集成...")
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        # 获取搜索配置
        search_config = config.processors['ai_reply_processor']['search'].copy()
        mcp_config = config.ai.mcp.model_dump()

        # 合并配置
        search_config['mcp'] = mcp_config
        
        logger.info(f"搜索配置: MCP启用={search_config.get('mcp', {}).get('enabled', False)}")
        
        # 创建搜索处理器
        search_handler = SearchHandler(search_config)
        
        # 测试搜索功能
        test_queries = [
            "人工智能在医疗领域的应用",
            "区块链技术发展趋势"
        ]
        
        for query in test_queries:
            logger.info(f"测试搜索查询: {query}")
            
            try:
                # 执行搜索
                results = await search_handler.search(query)
                
                if results:
                    logger.success(f"✅ 搜索成功，找到 {len(results)} 个结果:")
                    for i, result in enumerate(results[:3], 1):  # 只显示前3个结果
                        logger.info(f"  {i}. {result.get('title', 'N/A')}")
                        logger.info(f"     来源: {result.get('source', 'N/A')}")
                        logger.info(f"     URL: {result.get('url', 'N/A')}")
                        logger.info("")
                else:
                    logger.warning(f"⚠️ 搜索 '{query}' 未返回结果")
                    
            except Exception as e:
                logger.error(f"❌ 搜索 '{query}' 失败: {e}")
            
            # 等待一下避免请求过快
            await asyncio.sleep(2)
        
        logger.success("✅ AI Agent搜索处理器集成测试完成!")
        
    except Exception as e:
        logger.error(f"❌ AI Agent集成测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

async def test_search_quality_assessment():
    """测试搜索质量评估功能"""
    logger.info("开始测试搜索质量评估...")
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        # 获取搜索配置
        search_config = config.processors['ai_reply_processor']['search'].copy()
        mcp_config = config.ai.mcp.model_dump()
        search_config['mcp'] = mcp_config
        
        # 创建搜索处理器
        search_handler = SearchHandler(search_config)
        
        # 测试质量评估
        query = "Python机器学习库"
        logger.info(f"测试质量评估查询: {query}")
        
        # 执行搜索
        results = await search_handler.search(query)
        
        if results:
            logger.info(f"搜索到 {len(results)} 个结果，开始质量评估...")
            
            # 评估每个结果的质量
            for i, result in enumerate(results[:5], 1):
                quality_score = search_handler._calculate_quality_score(result, query)
                logger.info(f"结果 {i}: {result.get('title', 'N/A')[:50]}...")
                logger.info(f"  质量评分: {quality_score:.3f}")
                logger.info(f"  来源权威性: {result.get('source', 'N/A')}")
                logger.info("")
            
            logger.success("✅ 搜索质量评估测试完成!")
        else:
            logger.warning("⚠️ 未获取到搜索结果，无法进行质量评估")
            
    except Exception as e:
        logger.error(f"❌ 搜索质量评估测试失败: {e}")

async def test_fallback_mechanism():
    """测试备用搜索机制"""
    logger.info("开始测试备用搜索机制...")
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        # 获取搜索配置并禁用MCP
        search_config = config.processors['ai_reply_processor']['search'].copy()
        mcp_config = config.ai.mcp.model_dump()
        
        # 模拟MCP不可用的情况
        mcp_config['enabled'] = False
        search_config['mcp'] = mcp_config
        
        logger.info("模拟MCP不可用，测试备用搜索...")
        
        # 创建搜索处理器
        search_handler = SearchHandler(search_config)
        
        # 测试备用搜索
        query = "开源软件发展"
        logger.info(f"测试备用搜索查询: {query}")
        
        results = await search_handler.search(query)
        
        if results:
            logger.success(f"✅ 备用搜索成功，找到 {len(results)} 个结果")
            for i, result in enumerate(results[:2], 1):
                logger.info(f"  {i}. {result.get('title', 'N/A')}")
        else:
            logger.warning("⚠️ 备用搜索未返回结果")
            
        logger.success("✅ 备用搜索机制测试完成!")
        
    except Exception as e:
        logger.error(f"❌ 备用搜索机制测试失败: {e}")

async def main():
    """主测试函数"""
    logger.info("🚀 开始AI Agent系统与智谱AI搜索集成测试")
    logger.info("=" * 70)
    
    # 1. 测试搜索处理器集成
    await test_search_handler_integration()
    
    logger.info("=" * 70)
    
    # 2. 测试搜索质量评估
    await test_search_quality_assessment()
    
    logger.info("=" * 70)
    
    # 3. 测试备用搜索机制
    await test_fallback_mechanism()
    
    logger.info("=" * 70)
    logger.info("🎉 AI Agent系统集成测试完成!")

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    asyncio.run(main())
