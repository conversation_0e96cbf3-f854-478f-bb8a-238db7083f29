#!/usr/bin/env python3
"""
AI回复处理器测试脚本
测试新的AI Agent系统功能
"""

import os
import sys
import json
from typing import Dict, Any

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from loguru import logger
from src.processors.ai_reply_processor import AIReplyProcessor
from src.core.ai_analyzer import AnalysisResult


def create_test_email_data() -> Dict[str, Any]:
    """创建测试邮件数据"""
    return {
        "subject": "关于人工智能发展趋势的咨询",
        "sender": "<EMAIL>",
        "recipients": ["<EMAIL>"],
        "body": """
        您好！
        
        我想了解一下当前人工智能技术的发展趋势，特别是在以下几个方面：
        1. 大语言模型的最新进展
        2. AI在企业应用中的实际案例
        3. 未来5年的发展预测
        
        希望能得到详细的分析和建议。
        
        谢谢！
        """,
        "attachments": [],
        "date": "2024-01-15 10:30:00",
        "message_id": "test-message-001",
        "uid": "test-uid-001",
        "folder": "INBOX"
    }


def create_test_analysis_result() -> AnalysisResult:
    """创建测试分析结果"""
    return AnalysisResult(
        category="技术咨询",
        priority="中",
        sentiment="中性",
        summary="用户咨询人工智能发展趋势",
        action_required=True,
        suggested_actions=["提供详细分析", "搜索最新信息"],
        processor_suggestions=[
            {
                "processor_name": "ai_reply_processor",
                "reason": "需要AI生成详细回复",
                "priority": 1,
                "enabled": True
            }
        ],
        confidence=0.85
    )


def test_traditional_mode():
    """测试传统模式"""
    logger.info("=== 测试传统AI回复模式 ===")
    
    # 创建处理器配置（不包含新组件）
    config = {
        "enabled": True,
        "ai_prompt": "请根据以下邮件内容生成专业的回复："
    }
    
    # 创建处理器实例
    processor = AIReplyProcessor("ai_reply_processor", config)
    
    # 创建测试数据
    email_data = create_test_email_data()
    analysis = create_test_analysis_result()
    
    # 检查是否可以处理
    can_process = processor.can_process(email_data, analysis)
    logger.info(f"可以处理: {can_process}")
    
    if can_process:
        # 执行处理
        result = processor.process(email_data, analysis)
        logger.info("处理结果:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result if can_process else None


def test_enhanced_mode():
    """测试增强模式"""
    logger.info("=== 测试增强AI Agent模式 ===")
    
    # 创建处理器配置（包含新组件配置）
    config = {
        "enabled": True,
        "ai_prompt": "请根据以下邮件内容生成专业的回复：",
        "search": {
            "max_results": 5,
            "timeout": 30,
            "language": "zh",
            "region": "CN"
        },
        "analysis": {
            "max_content_length": 2000,
            "min_confidence_threshold": 0.6,
            "enable_deep_analysis": True
        },
        "task_execution": {
            "max_execution_time": 300,
            "enable_parallel_execution": False,
            "retry_failed_steps": True,
            "max_retries": 2
        },
        "response_generation": {
            "max_reply_length": 1000,
            "include_sources": True,
            "professional_tone": True,
            "language": "zh"
        }
    }
    
    # 创建处理器实例
    processor = AIReplyProcessor("ai_reply_processor", config)
    
    # 创建测试数据
    email_data = create_test_email_data()
    analysis = create_test_analysis_result()
    
    # 检查模式
    logger.info(f"使用增强模式: {processor.use_enhanced_mode}")
    
    # 检查是否可以处理
    can_process = processor.can_process(email_data, analysis)
    logger.info(f"可以处理: {can_process}")
    
    if can_process:
        # 执行处理
        result = processor.process(email_data, analysis)
        logger.info("处理结果:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result if can_process else None


def test_intent_classification():
    """测试意图识别功能"""
    logger.info("=== 测试意图识别功能 ===")
    
    try:
        from src.processors.ai_agent_components.utils.intent_classifier import IntentClassifier
        
        classifier = IntentClassifier()
        email_data = create_test_email_data()
        
        intent_result = classifier.classify_intent(email_data)
        
        logger.info("意图识别结果:")
        logger.info(f"意图类型: {intent_result.intent_type.value}")
        logger.info(f"置信度: {intent_result.confidence:.2f}")
        logger.info(f"任务类型: {intent_result.task_type.value}")
        logger.info(f"需要搜索: {intent_result.requires_search}")
        logger.info(f"关键词: {intent_result.keywords}")
        logger.info(f"复杂度: {intent_result.complexity_score:.2f}")
        logger.info(f"识别理由: {intent_result.reasoning}")
        
        return intent_result
        
    except ImportError as e:
        logger.error(f"无法导入意图分类器: {e}")
        return None


def test_search_handler():
    """测试搜索处理器功能"""
    logger.info("=== 测试搜索处理器功能 ===")
    
    try:
        from src.processors.ai_agent_components.components.search_handler import SearchHandler
        from src.processors.ai_agent_components.models.search_models import SearchType
        
        search_handler = SearchHandler()
        
        # 执行搜索
        search_context = search_handler.search("人工智能发展趋势", SearchType.GENERAL)
        
        logger.info("搜索结果:")
        logger.info(f"查询: {search_context.request.query}")
        logger.info(f"结果数量: {search_context.total_results}")
        logger.info(f"搜索时间: {search_context.search_time:.2f}秒")
        
        if search_context.results:
            logger.info("前3个结果:")
            for i, result in enumerate(search_context.get_top_results(3), 1):
                logger.info(f"{i}. {result.title} (评分: {result.relevance_score:.2f})")
                logger.info(f"   {result.snippet[:100]}...")
        
        return search_context
        
    except ImportError as e:
        logger.error(f"无法导入搜索处理器: {e}")
        return None


def main():
    """主测试函数"""
    logger.info("开始测试AI回复处理器系统")
    
    # 测试意图识别
    intent_result = test_intent_classification()
    
    # 测试搜索功能
    search_result = test_search_handler()
    
    # 测试传统模式
    traditional_result = test_traditional_mode()
    
    # 测试增强模式
    enhanced_result = test_enhanced_mode()
    
    logger.info("=== 测试总结 ===")
    logger.info(f"意图识别测试: {'成功' if intent_result else '失败'}")
    logger.info(f"搜索功能测试: {'成功' if search_result else '失败'}")
    logger.info(f"传统模式测试: {'成功' if traditional_result else '失败'}")
    logger.info(f"增强模式测试: {'成功' if enhanced_result else '失败'}")


if __name__ == "__main__":
    main()
