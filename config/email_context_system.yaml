# 邮件线程上下文系统配置

# 线程处理器配置
thread_context_processor:
  # 处理器名称
  name: "thread_context_processor"
  
  # 数据库配置
  thread_db_path: "data/threads.db"
  
  # 上下文策略配置
  context_strategy: "summary"  # 可选: full, summary, key_points
  
  # 澄清会话配置
  clarification_timeout: 24  # 小时
  
  # 上下文构建配置
  context_builders:
    summary:
      max_length: 500  # 摘要最大长度
    key_points:
      max_points: 5    # 关键点最大数量

# 数据库配置
database:
  # 自动清理配置
  cleanup:
    # 是否启用自动清理
    enabled: true
    # 清理频率（小时）
    interval: 24
    # 保留期限（天）
    retention_days: 30

# 邮件处理配置
email_processing:
  # 是否在回复中包含上下文
  include_context: true
  # 是否允许自动澄清
  allow_auto_clarification: true
  # 澄清阈值
  clarification_threshold: 0.8
  # 最大重试次数
  max_retry_attempts: 3

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  # 日志文件
  file: "logs/email_context_system.log"
  # 是否记录详细信息
  verbose: false

# 监控配置
monitoring:
  # 统计信息收集
  stats:
    enabled: true
    interval: 300  # 秒
  # 健康检查
  health_check:
    enabled: true
    endpoint: "/health"
    interval: 60  # 秒

# 性能优化
performance:
  # 缓存配置
  cache:
    enabled: true
    max_size: 1000  # 条目数
    ttl: 3600  # 秒
  # 批处理配置
  batch:
    enabled: true
    max_size: 100
    timeout: 30  # 秒