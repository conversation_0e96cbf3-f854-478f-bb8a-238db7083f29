# 第一阶段实施计划：基础架构搭建

## 📋 阶段目标

在第一阶段（2周），我们将搭建新系统的基础架构，包括：
1. 创建新的项目结构
2. 设置Docker开发环境
3. 配置数据库和基础API
4. 创建前端框架

## 🏗️ 项目结构创建

### 1. 创建根目录结构
```bash
mkdir -p mailer-ai-agent/{backend,frontend,agent-runtime,database,docker,docs,scripts,tests}
```

### 2. 后端结构
```bash
cd mailer-ai-agent/backend
mkdir -p {api,core,models,database,utils}
mkdir -p api/{auth,email,agents,tasks,mcp}
mkdir -p database/{migrations,schemas}
mkdir -p models/{user,email,agent,task,mcp}
```

### 3. 前端结构
```bash
cd ../frontend
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir
mkdir -p src/{components,pages,hooks,services,store,utils}
mkdir -p src/components/{email,agents,tasks,common}
mkdir -p src/pages/{dashboard,emails,agents,skills,settings}
```

### 4. Agent运行时结构
```bash
cd ../agent-runtime
mkdir -p {sandbox,tools,security}
mkdir -p sandbox/{browser,terminal,filesystem}
mkdir -p tools/{web_search,file_manager,code_executor,api_integrations}
mkdir -p security
```

## 🐳 Docker开发环境

### 1. 主docker-compose.yml
```yaml
version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: mailer_ai_agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/mailer_ai_agent
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - ./logs:/app/logs

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules

  # Agent运行时
  agent-runtime:
    build:
      context: ./agent-runtime
      dockerfile: Dockerfile
    environment:
      - BACKEND_URL=http://backend:8000
    depends_on:
      - backend
    volumes:
      - ./agent-runtime:/app
      - /var/run/docker.sock:/var/run/docker.sock

volumes:
  postgres_data:
  redis_data:
```

### 2. 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

### 3. 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npm", "run", "dev"]
```

## 🗄️ 数据库设计

### 1. 核心表结构
```sql
-- database/schemas/001_initial_schema.sql

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 邮箱账户表
CREATE TABLE email_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    username VARCHAR(255) NOT NULL,
    encrypted_password TEXT NOT NULL,
    use_ssl BOOLEAN DEFAULT true,
    protocol VARCHAR(10) DEFAULT 'imap',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 邮件表
CREATE TABLE emails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES email_accounts(id) ON DELETE CASCADE,
    message_id VARCHAR(255) NOT NULL,
    subject TEXT,
    sender VARCHAR(255),
    recipients TEXT[],
    body_text TEXT,
    body_html TEXT,
    received_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE,
    is_processed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Agent表
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    email_account_id UUID REFERENCES email_accounts(id),
    ai_provider VARCHAR(50) NOT NULL,
    ai_model VARCHAR(100) NOT NULL,
    ai_config JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agent技能表
CREATE TABLE agent_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    skill_name VARCHAR(100) NOT NULL,
    skill_type VARCHAR(50) NOT NULL,
    config JSONB DEFAULT '{}',
    is_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 任务表
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    email_id UUID REFERENCES emails(id),
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    input_data JSONB DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MCP服务表
CREATE TABLE mcp_services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    server_url VARCHAR(500) NOT NULL,
    api_key_encrypted TEXT,
    config JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_emails_account_id ON emails(account_id);
CREATE INDEX idx_emails_processed ON emails(is_processed);
CREATE INDEX idx_emails_received_at ON emails(received_at);
CREATE INDEX idx_tasks_agent_id ON tasks(agent_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_agent_skills_agent_id ON agent_skills(agent_id);
```

## 🚀 基础API框架

### 1. 后端主应用
```python
# backend/main.py
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer
import uvicorn

from api.auth import router as auth_router
from api.email import router as email_router
from api.agents import router as agents_router
from api.tasks import router as tasks_router
from api.mcp import router as mcp_router
from core.database import engine, Base
from core.config import settings

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Mailer AI Agent API",
    description="通过邮件协同工作的通用AI Agent系统",
    version="1.0.0"
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 路由注册
app.include_router(auth_router, prefix="/api/auth", tags=["认证"])
app.include_router(email_router, prefix="/api/email", tags=["邮件"])
app.include_router(agents_router, prefix="/api/agents", tags=["Agent"])
app.include_router(tasks_router, prefix="/api/tasks", tags=["任务"])
app.include_router(mcp_router, prefix="/api/mcp", tags=["MCP"])

@app.get("/")
async def root():
    return {"message": "Mailer AI Agent API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 2. 配置管理
```python
# backend/core/config.py
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 数据库配置
    database_url: str = "postgresql://postgres:postgres@localhost:5432/mailer_ai_agent"
    
    # Redis配置
    redis_url: str = "redis://localhost:6379"
    
    # JWT配置
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # AI配置
    default_ai_provider: str = "openai"
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    
    # MCP配置
    mcp_enabled: bool = True
    zhipu_api_key: Optional[str] = None
    
    class Config:
        env_file = ".env"

settings = Settings()
```

## 📱 前端框架

### 1. 主页面布局
```typescript
// frontend/src/app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Mailer AI Agent',
  description: '通过邮件协同工作的通用AI Agent系统',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body className={inter.className}>
        <div className="min-h-screen bg-gray-50">
          {children}
        </div>
      </body>
    </html>
  )
}
```

### 2. 仪表板页面
```typescript
// frontend/src/app/dashboard/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function Dashboard() {
  const [stats, setStats] = useState({
    totalEmails: 0,
    processedEmails: 0,
    activeAgents: 0,
    runningTasks: 0
  })

  useEffect(() => {
    // 获取统计数据
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats')
      const data = await response.json()
      setStats(data)
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">仪表板</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>总邮件数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalEmails}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>已处理邮件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.processedEmails}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>活跃Agent</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeAgents}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>运行中任务</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.runningTasks}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
```

## 📋 第一阶段检查清单

### 项目结构
- [ ] 创建根目录和子目录结构
- [ ] 设置Git仓库和.gitignore
- [ ] 创建README和文档结构

### Docker环境
- [ ] 编写docker-compose.yml
- [ ] 创建各服务的Dockerfile
- [ ] 配置开发环境变量
- [ ] 测试容器启动和通信

### 数据库
- [ ] 设计数据库表结构
- [ ] 创建迁移脚本
- [ ] 配置数据库连接
- [ ] 测试数据库操作

### 后端API
- [ ] 搭建FastAPI框架
- [ ] 实现基础路由结构
- [ ] 配置CORS和中间件
- [ ] 实现健康检查接口

### 前端框架
- [ ] 创建Next.js应用
- [ ] 配置Tailwind CSS
- [ ] 实现基础页面结构
- [ ] 配置API客户端

### 测试验证
- [ ] 验证所有服务能正常启动
- [ ] 测试前后端通信
- [ ] 验证数据库连接
- [ ] 检查日志输出

完成第一阶段后，我们将有一个完整的基础架构，可以开始第二阶段的核心功能迁移。
