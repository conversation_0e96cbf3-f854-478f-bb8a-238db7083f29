# Mailer AI Agent 系统迁移指南

## 📋 迁移概述

本指南详细说明如何将现有的邮件处理机器人逐步迁移到新的AI Agent系统架构。

## 🔄 迁移策略

### 渐进式迁移原则
1. **保持服务连续性**: 在迁移过程中确保现有功能正常运行
2. **向后兼容**: 新系统支持现有配置格式和API
3. **数据完整性**: 确保邮件数据和配置不丢失
4. **功能对等**: 新系统包含所有现有功能
5. **平滑过渡**: 提供迁移工具和自动化脚本

### 迁移阶段规划
```
阶段1: 并行开发 → 阶段2: 功能迁移 → 阶段3: 数据迁移 → 阶段4: 切换上线
```

## 🗂️ 代码迁移映射

### 核心模块迁移
| 现有模块 | 新架构位置 | 迁移方式 |
|---------|-----------|---------|
| `src/core/config_manager.py` | `backend/core/config_service.py` | 重构为微服务 |
| `src/core/email_client.py` | `backend/core/email_service.py` | 增强功能 |
| `src/core/ai_analyzer.py` | `backend/core/ai_service.py` | 模块化重构 |
| `src/core/processor_manager.py` | `backend/core/agent_manager.py` | 升级为Agent管理 |
| `src/core/scheduler.py` | `backend/core/task_scheduler.py` | 分布式任务队列 |

### 处理器迁移
| 现有处理器 | 新架构位置 | 迁移方式 |
|-----------|-----------|---------|
| `ai_reply_processor.py` | `backend/agents/skills/ai_reply_skill.py` | 转换为技能 |
| `pdf_processor.py` | `backend/agents/skills/pdf_skill.py` | 转换为技能 |
| `auto_reply_processor.py` | `backend/agents/skills/auto_reply_skill.py` | 转换为技能 |
| `notification_processor.py` | `backend/agents/skills/notification_skill.py` | 转换为技能 |

### MCP组件迁移
| 现有组件 | 新架构位置 | 迁移方式 |
|---------|-----------|---------|
| `ai_agent_components/components/mcp_client.py` | `backend/core/mcp_client.py` | 增强和标准化 |
| `ai_agent_components/components/search_handler.py` | `backend/agents/tools/search_tool.py` | 转换为工具 |
| `ai_agent_components/models/` | `backend/models/mcp/` | 数据模型重构 |

## 📊 数据迁移计划

### 配置数据迁移
```python
# 现有配置格式 (YAML)
email:
  host: "imap.gmail.com"
  username: "<EMAIL>"
  password: "password"

# 新配置格式 (数据库)
INSERT INTO email_accounts (user_id, provider, host, username, encrypted_password)
VALUES (1, 'gmail', 'imap.gmail.com', '<EMAIL>', encrypt('password'));
```

### 邮件数据迁移
```python
# 迁移脚本示例
def migrate_email_data():
    # 1. 读取现有邮件日志
    # 2. 解析邮件元数据
    # 3. 导入到新数据库
    # 4. 建立索引和关联
    pass
```

### 处理器配置迁移
```python
# 现有处理器配置
processors:
  ai_reply_processor:
    enabled: true
    config:
      search:
        max_results: 5

# 新Agent技能配置
INSERT INTO agent_skills (agent_id, skill_name, enabled, config)
VALUES (1, 'ai_reply_skill', true, '{"search": {"max_results": 5}}');
```

## 🛠️ 技术栈迁移

### 后端技术栈
```python
# 现有技术栈
- Python 3.8+
- asyncio
- loguru
- pydantic
- PyYAML

# 新技术栈
- Python 3.11+
- FastAPI
- SQLAlchemy
- Alembic
- Celery
- Redis
- PostgreSQL
```

### 前端技术栈
```javascript
// 新增前端技术栈
- Next.js 14
- React 18
- TypeScript
- Tailwind CSS
- shadcn/ui
- Zustand
- TanStack Query
```

### 容器化技术栈
```yaml
# 新增容器化
- Docker
- Docker Compose
- Kubernetes (可选)
```

## 🔧 迁移工具和脚本

### 自动迁移脚本
```python
# scripts/migrate.py
import asyncio
from pathlib import Path
from backend.core.migration_service import MigrationService

async def main():
    migrator = MigrationService()
    
    # 1. 迁移配置文件
    await migrator.migrate_config('config/config.yaml')
    
    # 2. 迁移邮件数据
    await migrator.migrate_email_data('logs/')
    
    # 3. 迁移处理器配置
    await migrator.migrate_processors('src/processors/')
    
    # 4. 验证迁移结果
    await migrator.validate_migration()

if __name__ == "__main__":
    asyncio.run(main())
```

### 配置转换工具
```python
# scripts/config_converter.py
def convert_yaml_to_db(yaml_path: str, db_url: str):
    """将YAML配置转换为数据库配置"""
    # 读取YAML配置
    # 转换为数据库记录
    # 验证转换结果
    pass
```

### 数据验证工具
```python
# scripts/data_validator.py
def validate_migration(old_data_path: str, new_db_url: str):
    """验证迁移数据的完整性"""
    # 对比数据数量
    # 验证数据格式
    # 检查关联关系
    pass
```

## 📋 迁移检查清单

### 迁移前准备
- [ ] 备份现有数据和配置
- [ ] 设置新的开发环境
- [ ] 准备迁移脚本和工具
- [ ] 制定回滚计划

### 配置迁移
- [ ] 邮箱配置迁移
- [ ] AI服务配置迁移
- [ ] 处理器配置迁移
- [ ] 日志配置迁移
- [ ] MCP服务配置迁移

### 代码迁移
- [ ] 核心模块重构
- [ ] 处理器转换为技能
- [ ] MCP客户端升级
- [ ] 工具函数迁移
- [ ] 测试用例更新

### 数据迁移
- [ ] 邮件历史数据
- [ ] 处理日志数据
- [ ] 用户配置数据
- [ ] 任务执行记录

### 功能验证
- [ ] 邮件收发功能
- [ ] AI分析功能
- [ ] 处理器执行
- [ ] MCP服务调用
- [ ] 前端界面功能

### 性能测试
- [ ] 邮件处理性能
- [ ] 数据库查询性能
- [ ] API响应时间
- [ ] 并发处理能力

## 🚨 风险控制

### 潜在风险
1. **数据丢失**: 迁移过程中配置或邮件数据丢失
2. **功能缺失**: 新系统缺少某些现有功能
3. **性能下降**: 新架构性能不如现有系统
4. **兼容性问题**: 新系统与现有工具不兼容

### 风险缓解措施
1. **完整备份**: 迁移前完整备份所有数据
2. **并行运行**: 新旧系统并行运行一段时间
3. **分阶段迁移**: 逐步迁移，每个阶段都可回滚
4. **充分测试**: 每个功能都要经过充分测试

### 回滚计划
```bash
# 回滚脚本
#!/bin/bash
# 1. 停止新系统
docker-compose down

# 2. 恢复旧系统
cp backup/config.yaml config/config.yaml
python main.py

# 3. 恢复数据
# 根据具体情况恢复数据
```

## 📈 迁移时间表

### 第1周：准备阶段
- 环境搭建
- 工具准备
- 数据备份

### 第2-3周：开发阶段
- 新系统开发
- 迁移脚本编写
- 测试环境验证

### 第4周：迁移阶段
- 数据迁移
- 功能验证
- 性能测试

### 第5周：上线阶段
- 生产环境部署
- 监控和调优
- 文档更新

## 📞 支持和帮助

如果在迁移过程中遇到问题，请：
1. 查看迁移日志文件
2. 运行验证脚本
3. 参考故障排除文档
4. 联系技术支持团队

迁移完成后，请及时更新相关文档和培训材料。
