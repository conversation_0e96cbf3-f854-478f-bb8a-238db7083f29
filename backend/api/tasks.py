"""
任务管理API路由
处理任务的创建、调度、执行和监控
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from core.database import get_db
from core.security import verify_token
from models.task import Task, TaskExecution
from schemas.task import (
    TaskCreate,
    TaskUpdate,
    TaskResponse,
    TaskListResponse,
    TaskExecutionResponse,
    TaskScheduleRequest
)

logger = logging.getLogger(__name__)
router = APIRouter()

async def get_current_user_id(token: str = Depends(verify_token)) -> str:
    """获取当前用户ID"""
    payload = verify_token(token)
    return payload.get("sub")

@router.get("/", response_model=TaskListResponse, summary="获取任务列表")
async def get_tasks(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取任务列表"""
    try:
        query = db.query(Task).filter(Task.user_id == current_user_id)
        
        # 应用过滤条件
        if status_filter:
            query = query.filter(Task.status == status_filter)
        if task_type:
            query = query.filter(Task.task_type == task_type)
        if search:
            query = query.filter(
                Task.name.contains(search) | 
                Task.description.contains(search)
            )
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        tasks = query.offset(skip).limit(limit).all()
        
        return TaskListResponse(
            tasks=[TaskResponse.from_orm(task) for task in tasks],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务列表失败"
        )

@router.get("/{task_id}", response_model=TaskResponse, summary="获取任务详情")
async def get_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取任务详情"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        return TaskResponse.from_orm(task)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务详情失败"
        )

@router.post("/", response_model=TaskResponse, status_code=status.HTTP_201_CREATED, summary="创建任务")
async def create_task(
    task_data: TaskCreate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """创建任务"""
    try:
        new_task = Task(
            user_id=current_user_id,
            **task_data.dict()
        )
        
        db.add(new_task)
        db.commit()
        db.refresh(new_task)
        
        logger.info(f"任务创建成功: {new_task.id}")
        
        return TaskResponse.from_orm(new_task)
        
    except Exception as e:
        logger.error(f"任务创建失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务创建失败"
        )

@router.put("/{task_id}", response_model=TaskResponse, summary="更新任务")
async def update_task(
    task_id: str,
    task_data: TaskUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """更新任务"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        # 更新字段
        update_data = task_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(task, field, value)
        
        db.commit()
        db.refresh(task)
        
        logger.info(f"任务更新成功: {task.id}")
        
        return TaskResponse.from_orm(task)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务更新失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务更新失败"
        )

@router.delete("/{task_id}", summary="删除任务")
async def delete_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """删除任务"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        db.delete(task)
        db.commit()
        
        logger.info(f"任务删除成功: {task_id}")
        
        return {"message": "任务删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务删除失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务删除失败"
        )

@router.post("/{task_id}/execute", response_model=TaskExecutionResponse, summary="执行任务")
async def execute_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """立即执行任务"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        if not task.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务未激活"
            )
        
        # 创建执行记录
        execution = TaskExecution(
            task_id=task_id,
            user_id=current_user_id,
            status="running",
            trigger_type="manual"
        )
        
        db.add(execution)
        db.commit()
        db.refresh(execution)
        
        # TODO: 实现实际的任务执行逻辑
        # 这里应该调用任务执行引擎
        
        # 模拟执行结果
        execution.status = "completed"
        execution.result = {
            "message": "任务执行完成",
            "processed_items": 1,
            "execution_time": 2.3
        }
        
        db.commit()
        
        logger.info(f"任务执行完成: {task_id}, 执行ID: {execution.id}")
        
        return TaskExecutionResponse.from_orm(execution)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务执行失败"
        )

@router.post("/{task_id}/schedule", summary="调度任务")
async def schedule_task(
    task_id: str,
    schedule_data: TaskScheduleRequest,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """设置任务调度"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        # 更新调度配置
        task.schedule_config = schedule_data.dict()
        task.is_scheduled = True
        
        db.commit()
        
        logger.info(f"任务调度设置成功: {task_id}")
        
        return {"message": "任务调度设置成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务调度设置失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务调度设置失败"
        )

@router.get("/{task_id}/executions", summary="获取任务执行历史")
async def get_task_executions(
    task_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取任务执行历史"""
    try:
        # 验证任务所有权
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        # 查询执行历史
        executions = db.query(TaskExecution).filter(
            TaskExecution.task_id == task_id
        ).order_by(TaskExecution.created_at.desc()).offset(skip).limit(limit).all()
        
        return [TaskExecutionResponse.from_orm(execution) for execution in executions]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务执行历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务执行历史失败"
        )

@router.post("/{task_id}/activate", summary="激活任务")
async def activate_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """激活任务"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        task.is_active = True
        task.status = "active"
        db.commit()
        
        logger.info(f"任务激活成功: {task_id}")
        
        return {"message": "任务激活成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务激活失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务激活失败"
        )

@router.post("/{task_id}/deactivate", summary="停用任务")
async def deactivate_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """停用任务"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        task.is_active = False
        task.status = "inactive"
        db.commit()
        
        logger.info(f"任务停用成功: {task_id}")
        
        return {"message": "任务停用成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务停用失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务停用失败"
        )
