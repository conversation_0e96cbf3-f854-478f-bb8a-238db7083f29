"""
MCP服务管理API路由
处理MCP服务的注册、配置、调用和监控
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import logging

from core.database import get_db
from core.security import verify_token
from models.mcp import MCPService, MCPServiceCall
from schemas.mcp import (
    MCPServiceCreate,
    MCPServiceUpdate,
    MCPServiceResponse,
    MCPServiceListResponse,
    MCPServiceCallResponse,
    MCPServiceCallRequest
)

logger = logging.getLogger(__name__)
router = APIRouter()

async def get_current_user_id(token: str = Depends(verify_token)) -> str:
    """获取当前用户ID"""
    payload = verify_token(token)
    return payload.get("sub")

@router.get("/", response_model=MCPServiceListResponse, summary="获取MCP服务列表")
async def get_mcp_services(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    service_type: Optional[str] = Query(None, description="服务类型过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取MCP服务列表"""
    try:
        query = db.query(MCPService).filter(MCPService.user_id == current_user_id)
        
        # 应用过滤条件
        if status_filter:
            query = query.filter(MCPService.status == status_filter)
        if service_type:
            query = query.filter(MCPService.service_type == service_type)
        if search:
            query = query.filter(
                MCPService.name.contains(search) | 
                MCPService.description.contains(search)
            )
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        services = query.offset(skip).limit(limit).all()
        
        return MCPServiceListResponse(
            services=[MCPServiceResponse.from_orm(service) for service in services],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取MCP服务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取MCP服务列表失败"
        )

@router.get("/{service_id}", response_model=MCPServiceResponse, summary="获取MCP服务详情")
async def get_mcp_service(
    service_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取MCP服务详情"""
    try:
        service = db.query(MCPService).filter(
            MCPService.id == service_id,
            MCPService.user_id == current_user_id
        ).first()
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务不存在"
            )
        
        return MCPServiceResponse.from_orm(service)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取MCP服务详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取MCP服务详情失败"
        )

@router.post("/", response_model=MCPServiceResponse, status_code=status.HTTP_201_CREATED, summary="注册MCP服务")
async def create_mcp_service(
    service_data: MCPServiceCreate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """注册MCP服务"""
    try:
        # 检查服务名称是否已存在
        existing_service = db.query(MCPService).filter(
            MCPService.name == service_data.name,
            MCPService.user_id == current_user_id
        ).first()
        
        if existing_service:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="服务名称已存在"
            )
        
        new_service = MCPService(
            user_id=current_user_id,
            **service_data.dict()
        )
        
        db.add(new_service)
        db.commit()
        db.refresh(new_service)
        
        logger.info(f"MCP服务注册成功: {new_service.id}")
        
        return MCPServiceResponse.from_orm(new_service)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MCP服务注册失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="MCP服务注册失败"
        )

@router.put("/{service_id}", response_model=MCPServiceResponse, summary="更新MCP服务")
async def update_mcp_service(
    service_id: str,
    service_data: MCPServiceUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """更新MCP服务"""
    try:
        service = db.query(MCPService).filter(
            MCPService.id == service_id,
            MCPService.user_id == current_user_id
        ).first()
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务不存在"
            )
        
        # 更新字段
        update_data = service_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(service, field, value)
        
        db.commit()
        db.refresh(service)
        
        logger.info(f"MCP服务更新成功: {service.id}")
        
        return MCPServiceResponse.from_orm(service)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MCP服务更新失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="MCP服务更新失败"
        )

@router.delete("/{service_id}", summary="删除MCP服务")
async def delete_mcp_service(
    service_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """删除MCP服务"""
    try:
        service = db.query(MCPService).filter(
            MCPService.id == service_id,
            MCPService.user_id == current_user_id
        ).first()
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务不存在"
            )
        
        db.delete(service)
        db.commit()
        
        logger.info(f"MCP服务删除成功: {service_id}")
        
        return {"message": "MCP服务删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MCP服务删除失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="MCP服务删除失败"
        )

@router.post("/{service_id}/call", response_model=MCPServiceCallResponse, summary="调用MCP服务")
async def call_mcp_service(
    service_id: str,
    call_data: MCPServiceCallRequest,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """调用MCP服务"""
    try:
        service = db.query(MCPService).filter(
            MCPService.id == service_id,
            MCPService.user_id == current_user_id
        ).first()
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务不存在"
            )
        
        if not service.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="MCP服务未激活"
            )
        
        # 创建调用记录
        service_call = MCPServiceCall(
            service_id=service_id,
            user_id=current_user_id,
            method=call_data.method,
            parameters=call_data.parameters,
            status="running"
        )
        
        db.add(service_call)
        db.commit()
        db.refresh(service_call)
        
        # TODO: 实现实际的MCP服务调用逻辑
        # 这里应该调用MCP客户端
        
        # 模拟调用结果
        service_call.status = "completed"
        service_call.response_data = {
            "result": "MCP服务调用成功",
            "method": call_data.method,
            "execution_time": 0.5
        }
        
        # 更新服务统计
        service.call_count += 1
        service.last_called_at = service_call.created_at
        
        db.commit()
        
        logger.info(f"MCP服务调用完成: {service_id}, 调用ID: {service_call.id}")
        
        return MCPServiceCallResponse.from_orm(service_call)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MCP服务调用失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="MCP服务调用失败"
        )

@router.get("/{service_id}/calls", summary="获取MCP服务调用历史")
async def get_mcp_service_calls(
    service_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取MCP服务调用历史"""
    try:
        # 验证服务所有权
        service = db.query(MCPService).filter(
            MCPService.id == service_id,
            MCPService.user_id == current_user_id
        ).first()
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务不存在"
            )
        
        # 查询调用历史
        calls = db.query(MCPServiceCall).filter(
            MCPServiceCall.service_id == service_id
        ).order_by(MCPServiceCall.created_at.desc()).offset(skip).limit(limit).all()
        
        return [MCPServiceCallResponse.from_orm(call) for call in calls]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取MCP服务调用历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取MCP服务调用历史失败"
        )

@router.post("/{service_id}/test", summary="测试MCP服务连接")
async def test_mcp_service(
    service_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """测试MCP服务连接"""
    try:
        service = db.query(MCPService).filter(
            MCPService.id == service_id,
            MCPService.user_id == current_user_id
        ).first()
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务不存在"
            )
        
        # TODO: 实现实际的连接测试逻辑
        # 这里应该测试MCP服务的连接状态
        
        # 模拟测试结果
        test_result = {
            "status": "success",
            "message": "MCP服务连接正常",
            "response_time": 0.2,
            "available_methods": ["search", "analyze", "process"]
        }
        
        logger.info(f"MCP服务连接测试完成: {service_id}")
        
        return test_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MCP服务连接测试失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="MCP服务连接测试失败"
        )
