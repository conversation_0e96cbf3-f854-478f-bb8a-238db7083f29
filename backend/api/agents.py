"""
AI Agent管理API路由
处理Agent的创建、配置、执行和监控
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from core.database import get_db
from core.security import verify_token
from models.agent import Agent, AgentExecution
from schemas.agent import (
    AgentCreate,
    AgentUpdate,
    AgentResponse,
    AgentListResponse,
    AgentExecutionResponse,
    AgentExecuteRequest
)

logger = logging.getLogger(__name__)
router = APIRouter()

async def get_current_user_id(token: str = Depends(verify_token)) -> str:
    """获取当前用户ID"""
    payload = verify_token(token)
    return payload.get("sub")

@router.get("/", response_model=AgentListResponse, summary="获取Agent列表")
async def get_agents(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    category: Optional[str] = Query(None, description="分类过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取Agent列表"""
    try:
        query = db.query(Agent).filter(Agent.user_id == current_user_id)
        
        # 应用过滤条件
        if status_filter:
            query = query.filter(Agent.status == status_filter)
        if category:
            query = query.filter(Agent.category == category)
        if search:
            query = query.filter(
                Agent.name.contains(search) | 
                Agent.description.contains(search)
            )
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        agents = query.offset(skip).limit(limit).all()
        
        return AgentListResponse(
            agents=[AgentResponse.from_orm(agent) for agent in agents],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取Agent列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取Agent列表失败"
        )

@router.get("/{agent_id}", response_model=AgentResponse, summary="获取Agent详情")
async def get_agent(
    agent_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取Agent详情"""
    try:
        agent = db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.user_id == current_user_id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent不存在"
            )
        
        return AgentResponse.from_orm(agent)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Agent详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取Agent详情失败"
        )

@router.post("/", response_model=AgentResponse, status_code=status.HTTP_201_CREATED, summary="创建Agent")
async def create_agent(
    agent_data: AgentCreate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """创建Agent"""
    try:
        new_agent = Agent(
            user_id=current_user_id,
            **agent_data.dict()
        )
        
        db.add(new_agent)
        db.commit()
        db.refresh(new_agent)
        
        logger.info(f"Agent创建成功: {new_agent.id}")
        
        return AgentResponse.from_orm(new_agent)
        
    except Exception as e:
        logger.error(f"Agent创建失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Agent创建失败"
        )

@router.put("/{agent_id}", response_model=AgentResponse, summary="更新Agent")
async def update_agent(
    agent_id: str,
    agent_data: AgentUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """更新Agent"""
    try:
        agent = db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.user_id == current_user_id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent不存在"
            )
        
        # 更新字段
        update_data = agent_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(agent, field, value)
        
        db.commit()
        db.refresh(agent)
        
        logger.info(f"Agent更新成功: {agent.id}")
        
        return AgentResponse.from_orm(agent)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Agent更新失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Agent更新失败"
        )

@router.delete("/{agent_id}", summary="删除Agent")
async def delete_agent(
    agent_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """删除Agent"""
    try:
        agent = db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.user_id == current_user_id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent不存在"
            )
        
        db.delete(agent)
        db.commit()
        
        logger.info(f"Agent删除成功: {agent_id}")
        
        return {"message": "Agent删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Agent删除失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Agent删除失败"
        )

@router.post("/{agent_id}/execute", response_model=AgentExecutionResponse, summary="执行Agent")
async def execute_agent(
    agent_id: str,
    execution_data: AgentExecuteRequest,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """执行Agent"""
    try:
        agent = db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.user_id == current_user_id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent不存在"
            )
        
        if not agent.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Agent未激活"
            )
        
        # 创建执行记录
        execution = AgentExecution(
            agent_id=agent_id,
            user_id=current_user_id,
            input_data=execution_data.input_data,
            status="running"
        )
        
        db.add(execution)
        db.commit()
        db.refresh(execution)
        
        # TODO: 实现实际的Agent执行逻辑
        # 这里应该调用Agent执行引擎
        
        # 模拟执行结果
        execution.status = "completed"
        execution.output_data = {
            "result": "Agent执行完成",
            "processed_items": 1,
            "execution_time": 1.5
        }
        
        db.commit()
        
        logger.info(f"Agent执行完成: {agent_id}, 执行ID: {execution.id}")
        
        return AgentExecutionResponse.from_orm(execution)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Agent执行失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Agent执行失败"
        )

@router.get("/{agent_id}/executions", summary="获取Agent执行历史")
async def get_agent_executions(
    agent_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """获取Agent执行历史"""
    try:
        # 验证Agent所有权
        agent = db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.user_id == current_user_id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent不存在"
            )
        
        # 查询执行历史
        executions = db.query(AgentExecution).filter(
            AgentExecution.agent_id == agent_id
        ).order_by(AgentExecution.created_at.desc()).offset(skip).limit(limit).all()
        
        return [AgentExecutionResponse.from_orm(execution) for execution in executions]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Agent执行历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取Agent执行历史失败"
        )

@router.post("/{agent_id}/activate", summary="激活Agent")
async def activate_agent(
    agent_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """激活Agent"""
    try:
        agent = db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.user_id == current_user_id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent不存在"
            )
        
        agent.is_active = True
        agent.status = "active"
        db.commit()
        
        logger.info(f"Agent激活成功: {agent_id}")
        
        return {"message": "Agent激活成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Agent激活失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Agent激活失败"
        )

@router.post("/{agent_id}/deactivate", summary="停用Agent")
async def deactivate_agent(
    agent_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
):
    """停用Agent"""
    try:
        agent = db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.user_id == current_user_id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent不存在"
            )
        
        agent.is_active = False
        agent.status = "inactive"
        db.commit()
        
        logger.info(f"Agent停用成功: {agent_id}")
        
        return {"message": "Agent停用成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Agent停用失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Agent停用失败"
        )
