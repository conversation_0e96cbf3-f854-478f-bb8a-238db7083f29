"""
Pydantic数据模式包
定义API请求和响应的数据结构
"""

from .auth import *
from .email import *
from .agent import *
from .task import *
from .mcp import *

__all__ = [
    # 认证相关
    "UserRegister",
    "UserLogin", 
    "UserResponse",
    "TokenResponse",
    "RefreshTokenRequest",
    
    # 邮件相关
    "EmailCreate",
    "EmailUpdate",
    "EmailResponse",
    "EmailListResponse",
    
    # Agent相关
    "AgentCreate",
    "AgentUpdate", 
    "AgentResponse",
    "AgentListResponse",
    
    # 任务相关
    "TaskCreate",
    "TaskUpdate",
    "TaskResponse", 
    "TaskListResponse",
    
    # MCP相关
    "MCPServiceCreate",
    "MCPServiceUpdate",
    "MCPServiceResponse",
    "MCPServiceListResponse",
]
