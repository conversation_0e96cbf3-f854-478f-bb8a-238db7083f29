"""
邮件相关的Pydantic数据模式
"""

from pydantic import BaseModel, EmailStr, validator
from datetime import datetime
from typing import Optional, List, Dict, Any

class EmailCreate(BaseModel):
    """邮件创建请求模式"""
    subject: str
    content: str
    sender_email: EmailStr
    sender_name: Optional[str] = None
    recipient_email: EmailStr
    recipient_name: Optional[str] = None
    folder: str = "inbox"
    priority: str = "medium"
    
    @validator('priority')
    def validate_priority(cls, v):
        if v not in ['low', 'medium', 'high', 'urgent']:
            raise ValueError('优先级必须是 low, medium, high, urgent 之一')
        return v
    
    @validator('folder')
    def validate_folder(cls, v):
        allowed_folders = ['inbox', 'sent', 'draft', 'trash', 'archived', 'spam']
        if v not in allowed_folders:
            raise ValueError(f'文件夹必须是 {", ".join(allowed_folders)} 之一')
        return v

class EmailUpdate(BaseModel):
    """邮件更新请求模式"""
    subject: Optional[str] = None
    content: Optional[str] = None
    folder: Optional[str] = None
    priority: Optional[str] = None
    status: Optional[str] = None
    tags: Optional[List[str]] = None
    
    @validator('priority')
    def validate_priority(cls, v):
        if v is not None and v not in ['low', 'medium', 'high', 'urgent']:
            raise ValueError('优先级必须是 low, medium, high, urgent 之一')
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None and v not in ['unread', 'read', 'replied', 'forwarded', 'archived', 'deleted']:
            raise ValueError('状态必须是 unread, read, replied, forwarded, archived, deleted 之一')
        return v

class EmailAttachmentResponse(BaseModel):
    """邮件附件响应模式"""
    id: str
    filename: str
    content_type: str
    file_size: int
    file_path: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class EmailResponse(BaseModel):
    """邮件响应模式"""
    id: str
    user_id: str
    subject: str
    content: str
    sender_email: str
    sender_name: Optional[str] = None
    recipient_email: str
    recipient_name: Optional[str] = None
    folder: str
    priority: str
    status: str
    tags: Optional[List[str]] = None
    attachments: Optional[List[EmailAttachmentResponse]] = None
    analysis_result: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
    received_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class EmailListResponse(BaseModel):
    """邮件列表响应模式"""
    emails: List[EmailResponse]
    total: int
    skip: int
    limit: int

class EmailAnalysisResponse(BaseModel):
    """邮件分析响应模式"""
    sentiment: str  # positive, negative, neutral
    category: str   # work, personal, promotion, spam, etc.
    priority: str   # low, medium, high, urgent
    suggested_actions: List[str]  # reply, forward, archive, delete, etc.
    summary: str
    keywords: List[str]
    confidence_score: Optional[float] = None
    
    @validator('sentiment')
    def validate_sentiment(cls, v):
        if v not in ['positive', 'negative', 'neutral']:
            raise ValueError('情感必须是 positive, negative, neutral 之一')
        return v

class EmailSearchRequest(BaseModel):
    """邮件搜索请求模式"""
    query: str
    folder: Optional[str] = None
    sender: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    has_attachments: Optional[bool] = None
    priority: Optional[str] = None
    status: Optional[str] = None
    tags: Optional[List[str]] = None

class EmailBatchOperation(BaseModel):
    """邮件批量操作模式"""
    email_ids: List[str]
    operation: str  # mark_read, mark_unread, archive, delete, move_to_folder
    target_folder: Optional[str] = None
    
    @validator('operation')
    def validate_operation(cls, v):
        allowed_ops = ['mark_read', 'mark_unread', 'archive', 'delete', 'move_to_folder', 'add_tag', 'remove_tag']
        if v not in allowed_ops:
            raise ValueError(f'操作必须是 {", ".join(allowed_ops)} 之一')
        return v

class EmailReplyRequest(BaseModel):
    """邮件回复请求模式"""
    content: str
    reply_to_all: bool = False
    attachments: Optional[List[str]] = None

class EmailForwardRequest(BaseModel):
    """邮件转发请求模式"""
    to_emails: List[EmailStr]
    content: Optional[str] = None
    include_attachments: bool = True

class EmailDraftRequest(BaseModel):
    """邮件草稿请求模式"""
    to_emails: List[EmailStr]
    cc_emails: Optional[List[EmailStr]] = None
    bcc_emails: Optional[List[EmailStr]] = None
    subject: str
    content: str
    attachments: Optional[List[str]] = None
    send_at: Optional[datetime] = None  # 定时发送

class EmailTemplateResponse(BaseModel):
    """邮件模板响应模式"""
    id: str
    name: str
    subject: str
    content: str
    category: str
    variables: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class EmailStatsResponse(BaseModel):
    """邮件统计响应模式"""
    total_emails: int
    unread_count: int
    today_count: int
    this_week_count: int
    this_month_count: int
    by_folder: Dict[str, int]
    by_priority: Dict[str, int]
    by_status: Dict[str, int]
