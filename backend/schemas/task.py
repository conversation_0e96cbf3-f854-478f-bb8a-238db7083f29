"""
任务相关的Pydantic数据模式
"""

from pydantic import BaseModel, validator
from datetime import datetime
from typing import Optional, List, Dict, Any

class TaskCreate(BaseModel):
    """任务创建请求模式"""
    name: str
    description: Optional[str] = None
    task_type: str
    config: Optional[Dict[str, Any]] = None
    input_schema: Optional[Dict[str, Any]] = None
    output_schema: Optional[Dict[str, Any]] = None
    schedule_config: Optional[Dict[str, Any]] = None
    is_scheduled: bool = False
    cron_expression: Optional[str] = None
    dependencies: Optional[List[str]] = None
    priority: int = 5
    timeout_seconds: Optional[int] = None
    max_retries: int = 3
    notification_config: Optional[Dict[str, Any]] = None
    
    @validator('name')
    def validate_name(cls, v):
        if len(v) < 2:
            raise ValueError('任务名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('任务名称不能超过200个字符')
        return v
    
    @validator('task_type')
    def validate_task_type(cls, v):
        allowed_types = [
            'email_processing', 'data_analysis', 'file_processing',
            'api_call', 'notification', 'backup', 'cleanup', 'custom'
        ]
        if v not in allowed_types:
            raise ValueError(f'任务类型必须是 {", ".join(allowed_types)} 之一')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        if v < 1 or v > 10:
            raise ValueError('优先级必须在1-10之间')
        return v

class TaskUpdate(BaseModel):
    """任务更新请求模式"""
    name: Optional[str] = None
    description: Optional[str] = None
    task_type: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    input_schema: Optional[Dict[str, Any]] = None
    output_schema: Optional[Dict[str, Any]] = None
    schedule_config: Optional[Dict[str, Any]] = None
    is_scheduled: Optional[bool] = None
    cron_expression: Optional[str] = None
    dependencies: Optional[List[str]] = None
    priority: Optional[int] = None
    timeout_seconds: Optional[int] = None
    max_retries: Optional[int] = None
    notification_config: Optional[Dict[str, Any]] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None:
            if len(v) < 2:
                raise ValueError('任务名称至少需要2个字符')
            if len(v) > 200:
                raise ValueError('任务名称不能超过200个字符')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        if v is not None and (v < 1 or v > 10):
            raise ValueError('优先级必须在1-10之间')
        return v

class TaskResponse(BaseModel):
    """任务响应模式"""
    id: str
    user_id: str
    name: str
    description: Optional[str] = None
    task_type: str
    config: Optional[Dict[str, Any]] = None
    input_schema: Optional[Dict[str, Any]] = None
    output_schema: Optional[Dict[str, Any]] = None
    schedule_config: Optional[Dict[str, Any]] = None
    is_scheduled: bool
    cron_expression: Optional[str] = None
    status: str
    is_active: bool
    execution_count: int
    success_count: int
    failure_count: int
    avg_execution_time: Optional[str] = None
    last_execution_at: Optional[datetime] = None
    next_execution_at: Optional[datetime] = None
    dependencies: Optional[List[str]] = None
    priority: int
    timeout_seconds: Optional[int] = None
    retry_count: int
    max_retries: int
    notification_config: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class TaskListResponse(BaseModel):
    """任务列表响应模式"""
    tasks: List[TaskResponse]
    total: int
    skip: int
    limit: int

class TaskExecutionResponse(BaseModel):
    """任务执行响应模式"""
    id: str
    task_id: str
    user_id: str
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    result: Optional[Dict[str, Any]] = None
    status: str
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    trigger_type: Optional[str] = None
    trigger_data: Optional[Dict[str, Any]] = None
    execution_time: Optional[str] = None
    memory_usage: Optional[str] = None
    cpu_usage: Optional[str] = None
    retry_count: int
    is_retry: bool
    parent_execution_id: Optional[str] = None
    environment: Optional[str] = None
    worker_id: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class TaskScheduleRequest(BaseModel):
    """任务调度请求模式"""
    cron_expression: str
    timezone: str = "UTC"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    max_runs: Optional[int] = None
    
    @validator('cron_expression')
    def validate_cron_expression(cls, v):
        # 简单的cron表达式验证
        parts = v.split()
        if len(parts) != 5:
            raise ValueError('Cron表达式必须包含5个部分')
        return v

class TaskTemplateResponse(BaseModel):
    """任务模板响应模式"""
    id: str
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    template_config: Dict[str, Any]
    default_schedule: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    author: Optional[str] = None
    version: str
    is_public: bool
    is_verified: bool
    usage_count: int
    rating: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class TaskStatsResponse(BaseModel):
    """任务统计响应模式"""
    total_tasks: int
    active_tasks: int
    scheduled_tasks: int
    total_executions: int
    successful_executions: int
    failed_executions: int
    avg_execution_time: Optional[float] = None
    by_type: Dict[str, int]
    by_status: Dict[str, int]
    recent_executions: List[TaskExecutionResponse]

class TaskBatchOperation(BaseModel):
    """任务批量操作模式"""
    task_ids: List[str]
    operation: str  # activate, deactivate, delete, execute
    config: Optional[Dict[str, Any]] = None
    
    @validator('operation')
    def validate_operation(cls, v):
        allowed_ops = ['activate', 'deactivate', 'delete', 'execute', 'clone']
        if v not in allowed_ops:
            raise ValueError(f'操作必须是 {", ".join(allowed_ops)} 之一')
        return v

class TaskDependency(BaseModel):
    """任务依赖定义"""
    task_id: str
    dependency_type: str  # success, completion, failure
    wait_for_completion: bool = True

class TaskNotificationConfig(BaseModel):
    """任务通知配置"""
    on_success: bool = False
    on_failure: bool = True
    on_retry: bool = False
    email_recipients: Optional[List[str]] = None
    webhook_url: Optional[str] = None
    slack_channel: Optional[str] = None

class TaskExecuteRequest(BaseModel):
    """任务执行请求模式"""
    input_data: Optional[Dict[str, Any]] = None
    override_config: Optional[Dict[str, Any]] = None
    priority_boost: Optional[int] = None
    
    @validator('priority_boost')
    def validate_priority_boost(cls, v):
        if v is not None and (v < -5 or v > 5):
            raise ValueError('优先级提升必须在-5到5之间')
        return v
