"""
认证相关的Pydantic数据模式
"""

from pydantic import BaseModel, EmailStr, validator
from datetime import datetime
from typing import Optional

class UserRegister(BaseModel):
    """用户注册请求模式"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('用户名至少需要3个字符')
        if len(v) > 50:
            raise ValueError('用户名不能超过50个字符')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('密码至少需要6个字符')
        if len(v) > 100:
            raise ValueError('密码不能超过100个字符')
        return v

class UserLogin(BaseModel):
    """用户登录请求模式"""
    email: EmailStr
    password: str
    device_type: Optional[str] = "web"
    device_name: Optional[str] = "Unknown Device"
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None

class UserResponse(BaseModel):
    """用户响应模式"""
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class TokenResponse(BaseModel):
    """令牌响应模式"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse

class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模式"""
    refresh_token: str

class PasswordChangeRequest(BaseModel):
    """密码修改请求模式"""
    current_password: str
    new_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 6:
            raise ValueError('新密码至少需要6个字符')
        if len(v) > 100:
            raise ValueError('新密码不能超过100个字符')
        return v

class PasswordResetRequest(BaseModel):
    """密码重置请求模式"""
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    """密码重置确认模式"""
    token: str
    new_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 6:
            raise ValueError('新密码至少需要6个字符')
        if len(v) > 100:
            raise ValueError('新密码不能超过100个字符')
        return v

class UserProfileUpdate(BaseModel):
    """用户资料更新模式"""
    username: Optional[str] = None
    full_name: Optional[str] = None
    
    @validator('username')
    def validate_username(cls, v):
        if v is not None:
            if len(v) < 3:
                raise ValueError('用户名至少需要3个字符')
            if len(v) > 50:
                raise ValueError('用户名不能超过50个字符')
        return v

class SessionResponse(BaseModel):
    """会话响应模式"""
    id: int
    device_type: str
    device_name: str
    ip_address: Optional[str] = None
    created_at: datetime
    last_used_at: datetime
    expires_at: datetime
    is_current: bool = False
    
    class Config:
        from_attributes = True
