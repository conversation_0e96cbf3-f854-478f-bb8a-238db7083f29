"""
AI Agent相关的Pydantic数据模式
"""

from pydantic import BaseModel, validator
from datetime import datetime
from typing import Optional, List, Dict, Any

class AgentCreate(BaseModel):
    """Agent创建请求模式"""
    name: str
    description: Optional[str] = None
    category: Optional[str] = "general"
    config: Optional[Dict[str, Any]] = None
    prompt_template: Optional[str] = None
    model_name: Optional[str] = "gpt-3.5-turbo"
    capabilities: Optional[List[str]] = None
    tools: Optional[List[str]] = None
    mcp_services: Optional[List[str]] = None
    is_public: bool = False
    
    @validator('name')
    def validate_name(cls, v):
        if len(v) < 2:
            raise ValueError('Agent名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('Agent名称不能超过200个字符')
        return v
    
    @validator('category')
    def validate_category(cls, v):
        if v is not None:
            allowed_categories = [
                'general', 'email', 'analysis', 'automation', 
                'customer_service', 'content', 'research', 'other'
            ]
            if v not in allowed_categories:
                raise ValueError(f'分类必须是 {", ".join(allowed_categories)} 之一')
        return v

class AgentUpdate(BaseModel):
    """Agent更新请求模式"""
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    prompt_template: Optional[str] = None
    model_name: Optional[str] = None
    capabilities: Optional[List[str]] = None
    tools: Optional[List[str]] = None
    mcp_services: Optional[List[str]] = None
    is_public: Optional[bool] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None:
            if len(v) < 2:
                raise ValueError('Agent名称至少需要2个字符')
            if len(v) > 200:
                raise ValueError('Agent名称不能超过200个字符')
        return v

class AgentResponse(BaseModel):
    """Agent响应模式"""
    id: str
    user_id: str
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    prompt_template: Optional[str] = None
    model_name: Optional[str] = None
    capabilities: Optional[List[str]] = None
    tools: Optional[List[str]] = None
    mcp_services: Optional[List[str]] = None
    status: str
    is_active: bool
    is_public: bool
    execution_count: int
    success_count: int
    failure_count: int
    avg_execution_time: Optional[str] = None
    last_execution_at: Optional[datetime] = None
    version: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class AgentListResponse(BaseModel):
    """Agent列表响应模式"""
    agents: List[AgentResponse]
    total: int
    skip: int
    limit: int

class AgentExecuteRequest(BaseModel):
    """Agent执行请求模式"""
    input_data: Dict[str, Any]
    environment: Optional[str] = "production"
    
    @validator('input_data')
    def validate_input_data(cls, v):
        if not v:
            raise ValueError('输入数据不能为空')
        return v

class AgentExecutionResponse(BaseModel):
    """Agent执行响应模式"""
    id: str
    agent_id: str
    user_id: str
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    status: str
    error_message: Optional[str] = None
    execution_time: Optional[str] = None
    tokens_used: Optional[int] = None
    cost: Optional[str] = None
    environment: Optional[str] = None
    model_version: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class AgentTemplateResponse(BaseModel):
    """Agent模板响应模式"""
    id: str
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    template_config: Dict[str, Any]
    default_prompt: Optional[str] = None
    tags: Optional[List[str]] = None
    author: Optional[str] = None
    version: str
    is_public: bool
    is_verified: bool
    usage_count: int
    rating: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class AgentStatsResponse(BaseModel):
    """Agent统计响应模式"""
    total_agents: int
    active_agents: int
    total_executions: int
    successful_executions: int
    failed_executions: int
    avg_execution_time: Optional[float] = None
    by_category: Dict[str, int]
    by_status: Dict[str, int]
    recent_executions: List[AgentExecutionResponse]

class AgentConfigTemplate(BaseModel):
    """Agent配置模板"""
    model_settings: Optional[Dict[str, Any]] = None
    prompt_settings: Optional[Dict[str, Any]] = None
    tool_settings: Optional[Dict[str, Any]] = None
    mcp_settings: Optional[Dict[str, Any]] = None
    execution_settings: Optional[Dict[str, Any]] = None

class AgentCapability(BaseModel):
    """Agent能力定义"""
    name: str
    description: str
    required_tools: Optional[List[str]] = None
    required_models: Optional[List[str]] = None
    parameters: Optional[Dict[str, Any]] = None

class AgentTool(BaseModel):
    """Agent工具定义"""
    name: str
    description: str
    tool_type: str  # builtin, mcp, custom
    config: Optional[Dict[str, Any]] = None
    required_permissions: Optional[List[str]] = None

class AgentBatchOperation(BaseModel):
    """Agent批量操作模式"""
    agent_ids: List[str]
    operation: str  # activate, deactivate, delete, update_config
    config: Optional[Dict[str, Any]] = None
    
    @validator('operation')
    def validate_operation(cls, v):
        allowed_ops = ['activate', 'deactivate', 'delete', 'update_config', 'clone']
        if v not in allowed_ops:
            raise ValueError(f'操作必须是 {", ".join(allowed_ops)} 之一')
        return v

class AgentCloneRequest(BaseModel):
    """Agent克隆请求模式"""
    new_name: str
    new_description: Optional[str] = None
    modify_config: Optional[Dict[str, Any]] = None
    
    @validator('new_name')
    def validate_new_name(cls, v):
        if len(v) < 2:
            raise ValueError('新Agent名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('新Agent名称不能超过200个字符')
        return v
