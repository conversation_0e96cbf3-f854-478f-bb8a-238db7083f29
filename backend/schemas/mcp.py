"""
MCP服务相关的Pydantic数据模式
"""

from pydantic import BaseModel, validator, HttpUrl
from datetime import datetime
from typing import Optional, List, Dict, Any

class MCPServiceCreate(BaseModel):
    """MCP服务创建请求模式"""
    name: str
    description: Optional[str] = None
    service_type: str
    connection_config: Dict[str, Any]
    endpoint_url: Optional[str] = None
    api_key: Optional[str] = None
    auth_type: str = "none"
    auth_config: Optional[Dict[str, Any]] = None
    capabilities: Optional[List[str]] = None
    available_methods: Optional[List[str]] = None
    timeout_seconds: int = 30
    retry_count: int = 3
    rate_limit: Optional[int] = None
    
    @validator('name')
    def validate_name(cls, v):
        if len(v) < 2:
            raise ValueError('服务名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('服务名称不能超过200个字符')
        return v
    
    @validator('service_type')
    def validate_service_type(cls, v):
        allowed_types = [
            'search', 'analysis', 'translation', 'generation',
            'processing', 'storage', 'notification', 'custom'
        ]
        if v not in allowed_types:
            raise ValueError(f'服务类型必须是 {", ".join(allowed_types)} 之一')
        return v
    
    @validator('auth_type')
    def validate_auth_type(cls, v):
        allowed_types = ['none', 'api_key', 'oauth2', 'basic', 'bearer']
        if v not in allowed_types:
            raise ValueError(f'认证类型必须是 {", ".join(allowed_types)} 之一')
        return v
    
    @validator('timeout_seconds')
    def validate_timeout(cls, v):
        if v < 1 or v > 300:
            raise ValueError('超时时间必须在1-300秒之间')
        return v

class MCPServiceUpdate(BaseModel):
    """MCP服务更新请求模式"""
    name: Optional[str] = None
    description: Optional[str] = None
    service_type: Optional[str] = None
    connection_config: Optional[Dict[str, Any]] = None
    endpoint_url: Optional[str] = None
    api_key: Optional[str] = None
    auth_type: Optional[str] = None
    auth_config: Optional[Dict[str, Any]] = None
    capabilities: Optional[List[str]] = None
    available_methods: Optional[List[str]] = None
    timeout_seconds: Optional[int] = None
    retry_count: Optional[int] = None
    rate_limit: Optional[int] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None:
            if len(v) < 2:
                raise ValueError('服务名称至少需要2个字符')
            if len(v) > 200:
                raise ValueError('服务名称不能超过200个字符')
        return v
    
    @validator('timeout_seconds')
    def validate_timeout(cls, v):
        if v is not None and (v < 1 or v > 300):
            raise ValueError('超时时间必须在1-300秒之间')
        return v

class MCPServiceResponse(BaseModel):
    """MCP服务响应模式"""
    id: str
    user_id: str
    name: str
    description: Optional[str] = None
    service_type: str
    connection_config: Dict[str, Any]
    endpoint_url: Optional[str] = None
    api_key: Optional[str] = None  # 已脱敏
    auth_type: str
    auth_config: Optional[Dict[str, Any]] = None
    capabilities: Optional[List[str]] = None
    available_methods: Optional[List[str]] = None
    status: str
    is_active: bool
    is_verified: bool
    call_count: int
    success_count: int
    failure_count: int
    avg_response_time: Optional[str] = None
    last_called_at: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    timeout_seconds: int
    retry_count: int
    rate_limit: Optional[int] = None
    version: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class MCPServiceListResponse(BaseModel):
    """MCP服务列表响应模式"""
    services: List[MCPServiceResponse]
    total: int
    skip: int
    limit: int

class MCPServiceCallRequest(BaseModel):
    """MCP服务调用请求模式"""
    method: str
    parameters: Optional[Dict[str, Any]] = None
    timeout_override: Optional[int] = None
    
    @validator('method')
    def validate_method(cls, v):
        if len(v) < 1:
            raise ValueError('方法名称不能为空')
        if len(v) > 100:
            raise ValueError('方法名称不能超过100个字符')
        return v

class MCPServiceCallResponse(BaseModel):
    """MCP服务调用响应模式"""
    id: str
    service_id: str
    user_id: str
    method: str
    parameters: Optional[Dict[str, Any]] = None
    response_data: Optional[Dict[str, Any]] = None
    status_code: Optional[int] = None
    status: str
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    response_time: Optional[str] = None
    request_size: Optional[int] = None
    response_size: Optional[int] = None
    retry_count: int
    is_retry: bool
    parent_call_id: Optional[str] = None
    client_ip: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class MCPServiceRegistryResponse(BaseModel):
    """MCP服务注册表响应模式"""
    id: str
    service_name: str
    service_description: Optional[str] = None
    service_category: Optional[str] = None
    provider_name: Optional[str] = None
    provider_url: Optional[str] = None
    config_template: Dict[str, Any]
    auth_template: Optional[Dict[str, Any]] = None
    supported_methods: Optional[List[str]] = None
    documentation_url: Optional[str] = None
    is_public: bool
    is_verified: bool
    is_deprecated: bool
    usage_count: int
    rating: Optional[str] = None
    version: str
    min_client_version: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class MCPServiceStatsResponse(BaseModel):
    """MCP服务统计响应模式"""
    total_services: int
    active_services: int
    verified_services: int
    total_calls: int
    successful_calls: int
    failed_calls: int
    avg_response_time: Optional[float] = None
    by_type: Dict[str, int]
    by_status: Dict[str, int]
    recent_calls: List[MCPServiceCallResponse]

class MCPServiceHealthCheck(BaseModel):
    """MCP服务健康检查响应"""
    service_id: str
    status: str  # healthy, unhealthy, unknown
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    last_check: datetime
    available_methods: Optional[List[str]] = None

class MCPServiceBatchOperation(BaseModel):
    """MCP服务批量操作模式"""
    service_ids: List[str]
    operation: str  # activate, deactivate, delete, health_check
    config: Optional[Dict[str, Any]] = None
    
    @validator('operation')
    def validate_operation(cls, v):
        allowed_ops = ['activate', 'deactivate', 'delete', 'health_check', 'update_config']
        if v not in allowed_ops:
            raise ValueError(f'操作必须是 {", ".join(allowed_ops)} 之一')
        return v

class MCPConnectionConfig(BaseModel):
    """MCP连接配置模式"""
    protocol: str = "http"  # http, https, websocket
    host: str
    port: Optional[int] = None
    path: Optional[str] = None
    ssl_verify: bool = True
    headers: Optional[Dict[str, str]] = None
    
    @validator('protocol')
    def validate_protocol(cls, v):
        allowed_protocols = ['http', 'https', 'websocket', 'tcp']
        if v not in allowed_protocols:
            raise ValueError(f'协议必须是 {", ".join(allowed_protocols)} 之一')
        return v

class MCPAuthConfig(BaseModel):
    """MCP认证配置模式"""
    type: str
    credentials: Dict[str, Any]
    refresh_token: Optional[str] = None
    expires_at: Optional[datetime] = None
    
    @validator('type')
    def validate_type(cls, v):
        allowed_types = ['none', 'api_key', 'oauth2', 'basic', 'bearer', 'jwt']
        if v not in allowed_types:
            raise ValueError(f'认证类型必须是 {", ".join(allowed_types)} 之一')
        return v

class MCPServiceDiscovery(BaseModel):
    """MCP服务发现响应"""
    services: List[Dict[str, Any]]
    total_found: int
    discovery_time: float
    source: str  # registry, network, manual
