"""
邮件模型
管理邮件账户、邮件数据和相关信息
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from core.database import Base

class EmailAccount(Base):
    """邮件账户模型"""
    __tablename__ = "email_accounts"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 用户关联
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="用户ID")
    
    # 账户基本信息
    name = Column(String(100), nullable=False, comment="账户名称")
    email_address = Column(String(255), nullable=False, comment="邮箱地址")
    provider = Column(String(50), nullable=False, comment="邮件提供商")
    
    # IMAP/POP3配置
    imap_host = Column(String(255), nullable=False, comment="IMAP主机")
    imap_port = Column(Integer, nullable=False, comment="IMAP端口")
    imap_username = Column(String(255), nullable=False, comment="IMAP用户名")
    imap_password_encrypted = Column(Text, nullable=False, comment="IMAP密码(加密)")
    imap_use_ssl = Column(Boolean, default=True, comment="IMAP是否使用SSL")
    
    # SMTP配置
    smtp_host = Column(String(255), nullable=True, comment="SMTP主机")
    smtp_port = Column(Integer, nullable=True, comment="SMTP端口")
    smtp_username = Column(String(255), nullable=True, comment="SMTP用户名")
    smtp_password_encrypted = Column(Text, nullable=True, comment="SMTP密码(加密)")
    smtp_use_tls = Column(Boolean, default=True, comment="SMTP是否使用TLS")
    
    # 协议设置
    protocol = Column(String(10), default="imap", comment="邮件协议")
    
    # 同步设置
    sync_enabled = Column(Boolean, default=True, comment="是否启用同步")
    sync_interval = Column(Integer, default=300, comment="同步间隔(秒)")
    last_sync_at = Column(DateTime(timezone=True), nullable=True, comment="最后同步时间")
    
    # 过滤设置
    folder_filter = Column(ARRAY(String), nullable=True, comment="文件夹过滤")
    sender_filter = Column(ARRAY(String), nullable=True, comment="发件人过滤")
    subject_filter = Column(ARRAY(String), nullable=True, comment="主题过滤")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    connection_status = Column(String(20), default="unknown", comment="连接状态")
    last_error = Column(Text, nullable=True, comment="最后错误信息")
    
    # 统计信息
    total_emails = Column(Integer, default=0, comment="总邮件数")
    unread_emails = Column(Integer, default=0, comment="未读邮件数")
    processed_emails = Column(Integer, default=0, comment="已处理邮件数")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    emails = relationship("Email", back_populates="account", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<EmailAccount(id={self.id}, name={self.name}, email={self.email_address})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "name": self.name,
            "email_address": self.email_address,
            "provider": self.provider,
            "imap_host": self.imap_host,
            "imap_port": self.imap_port,
            "imap_username": self.imap_username,
            "imap_use_ssl": self.imap_use_ssl,
            "smtp_host": self.smtp_host,
            "smtp_port": self.smtp_port,
            "smtp_username": self.smtp_username,
            "smtp_use_tls": self.smtp_use_tls,
            "protocol": self.protocol,
            "sync_enabled": self.sync_enabled,
            "sync_interval": self.sync_interval,
            "last_sync_at": self.last_sync_at.isoformat() if self.last_sync_at else None,
            "folder_filter": self.folder_filter,
            "sender_filter": self.sender_filter,
            "subject_filter": self.subject_filter,
            "is_active": self.is_active,
            "connection_status": self.connection_status,
            "last_error": self.last_error,
            "total_emails": self.total_emails,
            "unread_emails": self.unread_emails,
            "processed_emails": self.processed_emails,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

class Email(Base):
    """邮件模型"""
    __tablename__ = "emails"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 账户关联
    account_id = Column(UUID(as_uuid=True), ForeignKey("email_accounts.id", ondelete="CASCADE"), nullable=False, index=True, comment="邮件账户ID")
    
    # 邮件基本信息
    message_id = Column(String(255), nullable=False, index=True, comment="邮件消息ID")
    thread_id = Column(String(255), nullable=True, index=True, comment="邮件线程ID")
    
    # 邮件头信息
    subject = Column(Text, nullable=True, comment="邮件主题")
    sender = Column(String(255), nullable=True, comment="发件人")
    sender_name = Column(String(255), nullable=True, comment="发件人姓名")
    recipients = Column(ARRAY(String), nullable=True, comment="收件人列表")
    cc_recipients = Column(ARRAY(String), nullable=True, comment="抄送列表")
    bcc_recipients = Column(ARRAY(String), nullable=True, comment="密送列表")
    reply_to = Column(String(255), nullable=True, comment="回复地址")
    
    # 邮件内容
    body_text = Column(Text, nullable=True, comment="纯文本内容")
    body_html = Column(Text, nullable=True, comment="HTML内容")
    body_preview = Column(String(500), nullable=True, comment="内容预览")
    
    # 邮件属性
    priority = Column(String(10), default="normal", comment="优先级")
    importance = Column(String(10), default="normal", comment="重要性")
    sensitivity = Column(String(20), default="normal", comment="敏感性")
    
    # 附件信息
    has_attachments = Column(Boolean, default=False, comment="是否有附件")
    attachment_count = Column(Integer, default=0, comment="附件数量")
    attachment_info = Column(JSON, nullable=True, comment="附件信息")
    
    # 时间信息
    received_at = Column(DateTime(timezone=True), nullable=True, comment="接收时间")
    sent_at = Column(DateTime(timezone=True), nullable=True, comment="发送时间")
    
    # 处理状态
    is_read = Column(Boolean, default=False, comment="是否已读")
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    is_spam = Column(Boolean, default=False, comment="是否垃圾邮件")
    is_deleted = Column(Boolean, default=False, comment="是否已删除")
    
    # AI分析结果
    ai_analysis = Column(JSON, nullable=True, comment="AI分析结果")
    ai_category = Column(String(50), nullable=True, comment="AI分类")
    ai_sentiment = Column(String(20), nullable=True, comment="AI情感分析")
    ai_priority = Column(String(10), nullable=True, comment="AI优先级")
    ai_confidence = Column(String, nullable=True, comment="AI置信度")
    
    # 处理记录
    processed_at = Column(DateTime(timezone=True), nullable=True, comment="处理时间")
    processing_result = Column(JSON, nullable=True, comment="处理结果")
    processing_error = Column(Text, nullable=True, comment="处理错误")
    
    # 文件夹和标签
    folder = Column(String(100), nullable=True, comment="邮件文件夹")
    labels = Column(ARRAY(String), nullable=True, comment="邮件标签")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    account = relationship("EmailAccount", back_populates="emails")
    attachments = relationship("EmailAttachment", back_populates="email", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Email(id={self.id}, subject={self.subject}, sender={self.sender})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "account_id": str(self.account_id),
            "message_id": self.message_id,
            "thread_id": self.thread_id,
            "subject": self.subject,
            "sender": self.sender,
            "sender_name": self.sender_name,
            "recipients": self.recipients,
            "cc_recipients": self.cc_recipients,
            "bcc_recipients": self.bcc_recipients,
            "reply_to": self.reply_to,
            "body_text": self.body_text,
            "body_html": self.body_html,
            "body_preview": self.body_preview,
            "priority": self.priority,
            "importance": self.importance,
            "sensitivity": self.sensitivity,
            "has_attachments": self.has_attachments,
            "attachment_count": self.attachment_count,
            "attachment_info": self.attachment_info,
            "received_at": self.received_at.isoformat() if self.received_at else None,
            "sent_at": self.sent_at.isoformat() if self.sent_at else None,
            "is_read": self.is_read,
            "is_processed": self.is_processed,
            "is_spam": self.is_spam,
            "is_deleted": self.is_deleted,
            "ai_analysis": self.ai_analysis,
            "ai_category": self.ai_category,
            "ai_sentiment": self.ai_sentiment,
            "ai_priority": self.ai_priority,
            "ai_confidence": self.ai_confidence,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "processing_result": self.processing_result,
            "processing_error": self.processing_error,
            "folder": self.folder,
            "labels": self.labels,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

class EmailAttachment(Base):
    """邮件附件模型"""
    __tablename__ = "email_attachments"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 邮件关联
    email_id = Column(UUID(as_uuid=True), ForeignKey("emails.id", ondelete="CASCADE"), nullable=False, index=True, comment="邮件ID")
    
    # 附件信息
    filename = Column(String(255), nullable=False, comment="文件名")
    content_type = Column(String(100), nullable=True, comment="内容类型")
    size = Column(Integer, nullable=True, comment="文件大小")
    
    # 存储信息
    file_path = Column(String(500), nullable=True, comment="文件路径")
    file_hash = Column(String(64), nullable=True, comment="文件哈希")
    
    # 处理状态
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    processing_result = Column(JSON, nullable=True, comment="处理结果")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    email = relationship("Email", back_populates="attachments")
    
    def __repr__(self):
        return f"<EmailAttachment(id={self.id}, filename={self.filename}, size={self.size})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "email_id": str(self.email_id),
            "filename": self.filename,
            "content_type": self.content_type,
            "size": self.size,
            "file_path": self.file_path,
            "file_hash": self.file_hash,
            "is_processed": self.is_processed,
            "processing_result": self.processing_result,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
