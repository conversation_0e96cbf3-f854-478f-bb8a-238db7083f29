"""
用户模型
管理系统用户和认证信息
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from core.database import Base

class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 基本信息
    email = Column(String(255), unique=True, index=True, nullable=False, comment="邮箱地址")
    username = Column(String(100), unique=True, index=True, nullable=False, comment="用户名")
    full_name = Column(String(200), nullable=True, comment="全名")
    
    # 认证信息
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    is_verified = Column(Boolean, default=False, comment="是否已验证邮箱")
    
    # 个人设置
    timezone = Column(String(50), default="UTC", comment="时区")
    language = Column(String(10), default="zh", comment="语言偏好")
    theme = Column(String(20), default="light", comment="主题偏好")
    
    # 通知设置
    email_notifications = Column(Boolean, default=True, comment="是否接收邮件通知")
    push_notifications = Column(Boolean, default=True, comment="是否接收推送通知")
    
    # 安全设置
    two_factor_enabled = Column(Boolean, default=False, comment="是否启用双因子认证")
    two_factor_secret = Column(String(32), nullable=True, comment="双因子认证密钥")
    
    # 最后活动
    last_login_at = Column(DateTime(timezone=True), nullable=True, comment="最后登录时间")
    last_login_ip = Column(String(45), nullable=True, comment="最后登录IP")
    
    # 个人简介
    bio = Column(Text, nullable=True, comment="个人简介")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "email": self.email,
            "username": self.username,
            "full_name": self.full_name,
            "is_active": self.is_active,
            "is_superuser": self.is_superuser,
            "is_verified": self.is_verified,
            "timezone": self.timezone,
            "language": self.language,
            "theme": self.theme,
            "email_notifications": self.email_notifications,
            "push_notifications": self.push_notifications,
            "two_factor_enabled": self.two_factor_enabled,
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None,
            "last_login_ip": self.last_login_ip,
            "bio": self.bio,
            "avatar_url": self.avatar_url,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def to_public_dict(self):
        """转换为公开字典（不包含敏感信息）"""
        return {
            "id": str(self.id),
            "username": self.username,
            "full_name": self.full_name,
            "bio": self.bio,
            "avatar_url": self.avatar_url,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

class UserSession(Base):
    """用户会话模型"""
    __tablename__ = "user_sessions"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 用户ID
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 会话信息
    session_token = Column(String(255), unique=True, index=True, nullable=False, comment="会话令牌")
    refresh_token = Column(String(255), unique=True, index=True, nullable=True, comment="刷新令牌")
    
    # 设备信息
    device_type = Column(String(50), nullable=True, comment="设备类型")
    device_name = Column(String(200), nullable=True, comment="设备名称")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    
    # 地理位置
    country = Column(String(100), nullable=True, comment="国家")
    city = Column(String(100), nullable=True, comment="城市")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否活跃")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    last_used_at = Column(DateTime(timezone=True), server_default=func.now(), comment="最后使用时间")
    expires_at = Column(DateTime(timezone=True), nullable=False, comment="过期时间")
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, device_type={self.device_type})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "device_type": self.device_type,
            "device_name": self.device_name,
            "ip_address": self.ip_address,
            "country": self.country,
            "city": self.city,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
        }

class UserPreference(Base):
    """用户偏好设置模型"""
    __tablename__ = "user_preferences"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 用户ID
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 偏好设置
    preference_key = Column(String(100), nullable=False, comment="偏好键")
    preference_value = Column(Text, nullable=True, comment="偏好值")
    preference_type = Column(String(20), default="string", comment="偏好类型")
    
    # 分类
    category = Column(String(50), nullable=True, comment="分类")
    description = Column(Text, nullable=True, comment="描述")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<UserPreference(user_id={self.user_id}, key={self.preference_key}, value={self.preference_value})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "preference_key": self.preference_key,
            "preference_value": self.preference_value,
            "preference_type": self.preference_type,
            "category": self.category,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
