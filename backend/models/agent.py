"""
AI Agent模型
管理AI Agent的配置、状态和执行历史
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from core.database import Base

class Agent(Base):
    """AI Agent模型"""
    __tablename__ = "agents"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 用户ID
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 基本信息
    name = Column(String(200), nullable=False, comment="Agent名称")
    description = Column(Text, nullable=True, comment="Agent描述")
    category = Column(String(100), nullable=True, comment="Agent分类")
    
    # 配置信息
    config = Column(JSON, nullable=True, comment="Agent配置")
    prompt_template = Column(Text, nullable=True, comment="提示词模板")
    model_name = Column(String(100), nullable=True, comment="使用的模型名称")
    
    # 功能配置
    capabilities = Column(JSON, nullable=True, comment="Agent能力列表")
    tools = Column(JSON, nullable=True, comment="可用工具列表")
    mcp_services = Column(JSON, nullable=True, comment="MCP服务配置")
    
    # 状态信息
    status = Column(String(50), default="inactive", comment="Agent状态")
    is_active = Column(Boolean, default=False, comment="是否激活")
    is_public = Column(Boolean, default=False, comment="是否公开")
    
    # 执行统计
    execution_count = Column(Integer, default=0, comment="执行次数")
    success_count = Column(Integer, default=0, comment="成功次数")
    failure_count = Column(Integer, default=0, comment="失败次数")
    
    # 性能指标
    avg_execution_time = Column(String(50), nullable=True, comment="平均执行时间")
    last_execution_at = Column(DateTime(timezone=True), nullable=True, comment="最后执行时间")
    
    # 版本信息
    version = Column(String(20), default="1.0.0", comment="Agent版本")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<Agent(id={self.id}, name={self.name}, status={self.status})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "config": self.config,
            "prompt_template": self.prompt_template,
            "model_name": self.model_name,
            "capabilities": self.capabilities,
            "tools": self.tools,
            "mcp_services": self.mcp_services,
            "status": self.status,
            "is_active": self.is_active,
            "is_public": self.is_public,
            "execution_count": self.execution_count,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "avg_execution_time": self.avg_execution_time,
            "last_execution_at": self.last_execution_at.isoformat() if self.last_execution_at else None,
            "version": self.version,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

class AgentExecution(Base):
    """Agent执行记录模型"""
    __tablename__ = "agent_executions"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 关联ID
    agent_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="Agent ID")
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 执行信息
    input_data = Column(JSON, nullable=True, comment="输入数据")
    output_data = Column(JSON, nullable=True, comment="输出数据")
    
    # 状态信息
    status = Column(String(50), default="pending", comment="执行状态")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 性能指标
    execution_time = Column(String(50), nullable=True, comment="执行时间(秒)")
    tokens_used = Column(Integer, nullable=True, comment="使用的token数量")
    cost = Column(String(20), nullable=True, comment="执行成本")
    
    # 执行环境
    environment = Column(String(100), nullable=True, comment="执行环境")
    model_version = Column(String(50), nullable=True, comment="模型版本")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    def __repr__(self):
        return f"<AgentExecution(id={self.id}, agent_id={self.agent_id}, status={self.status})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "agent_id": str(self.agent_id),
            "user_id": str(self.user_id),
            "input_data": self.input_data,
            "output_data": self.output_data,
            "status": self.status,
            "error_message": self.error_message,
            "execution_time": self.execution_time,
            "tokens_used": self.tokens_used,
            "cost": self.cost,
            "environment": self.environment,
            "model_version": self.model_version,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
        }

class AgentTemplate(Base):
    """Agent模板模型"""
    __tablename__ = "agent_templates"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 基本信息
    name = Column(String(200), nullable=False, comment="模板名称")
    description = Column(Text, nullable=True, comment="模板描述")
    category = Column(String(100), nullable=True, comment="模板分类")
    
    # 模板配置
    template_config = Column(JSON, nullable=False, comment="模板配置")
    default_prompt = Column(Text, nullable=True, comment="默认提示词")
    
    # 元数据
    tags = Column(JSON, nullable=True, comment="标签")
    author = Column(String(200), nullable=True, comment="作者")
    version = Column(String(20), default="1.0.0", comment="模板版本")
    
    # 状态
    is_public = Column(Boolean, default=True, comment="是否公开")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    rating = Column(String(10), nullable=True, comment="评分")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<AgentTemplate(id={self.id}, name={self.name}, category={self.category})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "template_config": self.template_config,
            "default_prompt": self.default_prompt,
            "tags": self.tags,
            "author": self.author,
            "version": self.version,
            "is_public": self.is_public,
            "is_verified": self.is_verified,
            "usage_count": self.usage_count,
            "rating": self.rating,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
