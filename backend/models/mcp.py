"""
MCP服务模型
管理MCP服务的注册、配置和调用记录
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from core.database import Base

class MCPService(Base):
    """MCP服务模型"""
    __tablename__ = "mcp_services"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 用户ID
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 基本信息
    name = Column(String(200), nullable=False, comment="服务名称")
    description = Column(Text, nullable=True, comment="服务描述")
    service_type = Column(String(100), nullable=False, comment="服务类型")
    
    # 连接配置
    connection_config = Column(JSON, nullable=False, comment="连接配置")
    endpoint_url = Column(String(500), nullable=True, comment="服务端点URL")
    api_key = Column(String(255), nullable=True, comment="API密钥")
    
    # 认证配置
    auth_type = Column(String(50), default="none", comment="认证类型")
    auth_config = Column(JSON, nullable=True, comment="认证配置")
    
    # 功能配置
    capabilities = Column(JSON, nullable=True, comment="服务能力")
    available_methods = Column(JSON, nullable=True, comment="可用方法")
    
    # 状态信息
    status = Column(String(50), default="inactive", comment="服务状态")
    is_active = Column(Boolean, default=False, comment="是否激活")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    
    # 调用统计
    call_count = Column(Integer, default=0, comment="调用次数")
    success_count = Column(Integer, default=0, comment="成功次数")
    failure_count = Column(Integer, default=0, comment="失败次数")
    
    # 性能指标
    avg_response_time = Column(String(50), nullable=True, comment="平均响应时间")
    last_called_at = Column(DateTime(timezone=True), nullable=True, comment="最后调用时间")
    last_health_check = Column(DateTime(timezone=True), nullable=True, comment="最后健康检查时间")
    
    # 配置信息
    timeout_seconds = Column(Integer, default=30, comment="超时时间(秒)")
    retry_count = Column(Integer, default=3, comment="重试次数")
    rate_limit = Column(Integer, nullable=True, comment="速率限制(每分钟)")
    
    # 版本信息
    version = Column(String(20), default="1.0.0", comment="服务版本")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<MCPService(id={self.id}, name={self.name}, status={self.status})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "name": self.name,
            "description": self.description,
            "service_type": self.service_type,
            "connection_config": self.connection_config,
            "endpoint_url": self.endpoint_url,
            "api_key": "***" if self.api_key else None,  # 隐藏敏感信息
            "auth_type": self.auth_type,
            "auth_config": self.auth_config,
            "capabilities": self.capabilities,
            "available_methods": self.available_methods,
            "status": self.status,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "call_count": self.call_count,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "avg_response_time": self.avg_response_time,
            "last_called_at": self.last_called_at.isoformat() if self.last_called_at else None,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "timeout_seconds": self.timeout_seconds,
            "retry_count": self.retry_count,
            "rate_limit": self.rate_limit,
            "version": self.version,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

class MCPServiceCall(Base):
    """MCP服务调用记录模型"""
    __tablename__ = "mcp_service_calls"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 关联ID
    service_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="服务ID")
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 调用信息
    method = Column(String(100), nullable=False, comment="调用方法")
    parameters = Column(JSON, nullable=True, comment="调用参数")
    
    # 响应信息
    response_data = Column(JSON, nullable=True, comment="响应数据")
    status_code = Column(Integer, nullable=True, comment="HTTP状态码")
    
    # 状态信息
    status = Column(String(50), default="pending", comment="调用状态")
    error_message = Column(Text, nullable=True, comment="错误信息")
    error_details = Column(JSON, nullable=True, comment="错误详情")
    
    # 性能指标
    response_time = Column(String(50), nullable=True, comment="响应时间(秒)")
    request_size = Column(Integer, nullable=True, comment="请求大小(字节)")
    response_size = Column(Integer, nullable=True, comment="响应大小(字节)")
    
    # 重试信息
    retry_count = Column(Integer, default=0, comment="重试次数")
    is_retry = Column(Boolean, default=False, comment="是否为重试")
    parent_call_id = Column(UUID(as_uuid=True), nullable=True, comment="父调用ID")
    
    # 调用环境
    client_ip = Column(String(45), nullable=True, comment="客户端IP")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    def __repr__(self):
        return f"<MCPServiceCall(id={self.id}, service_id={self.service_id}, method={self.method}, status={self.status})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "service_id": str(self.service_id),
            "user_id": str(self.user_id),
            "method": self.method,
            "parameters": self.parameters,
            "response_data": self.response_data,
            "status_code": self.status_code,
            "status": self.status,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "response_time": self.response_time,
            "request_size": self.request_size,
            "response_size": self.response_size,
            "retry_count": self.retry_count,
            "is_retry": self.is_retry,
            "parent_call_id": str(self.parent_call_id) if self.parent_call_id else None,
            "client_ip": self.client_ip,
            "user_agent": self.user_agent,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
        }

class MCPServiceRegistry(Base):
    """MCP服务注册表模型"""
    __tablename__ = "mcp_service_registry"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 服务信息
    service_name = Column(String(200), nullable=False, unique=True, comment="服务名称")
    service_description = Column(Text, nullable=True, comment="服务描述")
    service_category = Column(String(100), nullable=True, comment="服务分类")
    
    # 提供商信息
    provider_name = Column(String(200), nullable=True, comment="提供商名称")
    provider_url = Column(String(500), nullable=True, comment="提供商URL")
    
    # 服务配置模板
    config_template = Column(JSON, nullable=False, comment="配置模板")
    auth_template = Column(JSON, nullable=True, comment="认证模板")
    
    # 服务元数据
    supported_methods = Column(JSON, nullable=True, comment="支持的方法")
    documentation_url = Column(String(500), nullable=True, comment="文档URL")
    
    # 状态信息
    is_public = Column(Boolean, default=True, comment="是否公开")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    is_deprecated = Column(Boolean, default=False, comment="是否已弃用")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    rating = Column(String(10), nullable=True, comment="评分")
    
    # 版本信息
    version = Column(String(20), default="1.0.0", comment="服务版本")
    min_client_version = Column(String(20), nullable=True, comment="最小客户端版本")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<MCPServiceRegistry(id={self.id}, service_name={self.service_name}, version={self.version})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "service_name": self.service_name,
            "service_description": self.service_description,
            "service_category": self.service_category,
            "provider_name": self.provider_name,
            "provider_url": self.provider_url,
            "config_template": self.config_template,
            "auth_template": self.auth_template,
            "supported_methods": self.supported_methods,
            "documentation_url": self.documentation_url,
            "is_public": self.is_public,
            "is_verified": self.is_verified,
            "is_deprecated": self.is_deprecated,
            "usage_count": self.usage_count,
            "rating": self.rating,
            "version": self.version,
            "min_client_version": self.min_client_version,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
