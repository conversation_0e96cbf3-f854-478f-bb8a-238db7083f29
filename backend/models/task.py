"""
任务模型
管理系统任务的定义、调度和执行
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from core.database import Base

class Task(Base):
    """任务模型"""
    __tablename__ = "tasks"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 用户ID
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 基本信息
    name = Column(String(200), nullable=False, comment="任务名称")
    description = Column(Text, nullable=True, comment="任务描述")
    task_type = Column(String(100), nullable=False, comment="任务类型")
    
    # 配置信息
    config = Column(JSON, nullable=True, comment="任务配置")
    input_schema = Column(JSON, nullable=True, comment="输入数据模式")
    output_schema = Column(JSON, nullable=True, comment="输出数据模式")
    
    # 调度配置
    schedule_config = Column(JSON, nullable=True, comment="调度配置")
    is_scheduled = Column(Boolean, default=False, comment="是否启用调度")
    cron_expression = Column(String(100), nullable=True, comment="Cron表达式")
    
    # 状态信息
    status = Column(String(50), default="inactive", comment="任务状态")
    is_active = Column(Boolean, default=False, comment="是否激活")
    
    # 执行统计
    execution_count = Column(Integer, default=0, comment="执行次数")
    success_count = Column(Integer, default=0, comment="成功次数")
    failure_count = Column(Integer, default=0, comment="失败次数")
    
    # 性能指标
    avg_execution_time = Column(String(50), nullable=True, comment="平均执行时间")
    last_execution_at = Column(DateTime(timezone=True), nullable=True, comment="最后执行时间")
    next_execution_at = Column(DateTime(timezone=True), nullable=True, comment="下次执行时间")
    
    # 依赖关系
    dependencies = Column(JSON, nullable=True, comment="任务依赖")
    priority = Column(Integer, default=5, comment="优先级(1-10)")
    
    # 超时设置
    timeout_seconds = Column(Integer, nullable=True, comment="超时时间(秒)")
    retry_count = Column(Integer, default=0, comment="重试次数")
    max_retries = Column(Integer, default=3, comment="最大重试次数")
    
    # 通知设置
    notification_config = Column(JSON, nullable=True, comment="通知配置")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<Task(id={self.id}, name={self.name}, status={self.status})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "name": self.name,
            "description": self.description,
            "task_type": self.task_type,
            "config": self.config,
            "input_schema": self.input_schema,
            "output_schema": self.output_schema,
            "schedule_config": self.schedule_config,
            "is_scheduled": self.is_scheduled,
            "cron_expression": self.cron_expression,
            "status": self.status,
            "is_active": self.is_active,
            "execution_count": self.execution_count,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "avg_execution_time": self.avg_execution_time,
            "last_execution_at": self.last_execution_at.isoformat() if self.last_execution_at else None,
            "next_execution_at": self.next_execution_at.isoformat() if self.next_execution_at else None,
            "dependencies": self.dependencies,
            "priority": self.priority,
            "timeout_seconds": self.timeout_seconds,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "notification_config": self.notification_config,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

class TaskExecution(Base):
    """任务执行记录模型"""
    __tablename__ = "task_executions"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 关联ID
    task_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="任务ID")
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, comment="用户ID")
    
    # 执行信息
    input_data = Column(JSON, nullable=True, comment="输入数据")
    output_data = Column(JSON, nullable=True, comment="输出数据")
    result = Column(JSON, nullable=True, comment="执行结果")
    
    # 状态信息
    status = Column(String(50), default="pending", comment="执行状态")
    error_message = Column(Text, nullable=True, comment="错误信息")
    error_details = Column(JSON, nullable=True, comment="错误详情")
    
    # 触发信息
    trigger_type = Column(String(50), nullable=True, comment="触发类型")
    trigger_data = Column(JSON, nullable=True, comment="触发数据")
    
    # 性能指标
    execution_time = Column(String(50), nullable=True, comment="执行时间(秒)")
    memory_usage = Column(String(50), nullable=True, comment="内存使用量")
    cpu_usage = Column(String(50), nullable=True, comment="CPU使用率")
    
    # 重试信息
    retry_count = Column(Integer, default=0, comment="重试次数")
    is_retry = Column(Boolean, default=False, comment="是否为重试")
    parent_execution_id = Column(UUID(as_uuid=True), nullable=True, comment="父执行ID")
    
    # 执行环境
    environment = Column(String(100), nullable=True, comment="执行环境")
    worker_id = Column(String(100), nullable=True, comment="工作节点ID")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    def __repr__(self):
        return f"<TaskExecution(id={self.id}, task_id={self.task_id}, status={self.status})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "task_id": str(self.task_id),
            "user_id": str(self.user_id),
            "input_data": self.input_data,
            "output_data": self.output_data,
            "result": self.result,
            "status": self.status,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "trigger_type": self.trigger_type,
            "trigger_data": self.trigger_data,
            "execution_time": self.execution_time,
            "memory_usage": self.memory_usage,
            "cpu_usage": self.cpu_usage,
            "retry_count": self.retry_count,
            "is_retry": self.is_retry,
            "parent_execution_id": str(self.parent_execution_id) if self.parent_execution_id else None,
            "environment": self.environment,
            "worker_id": self.worker_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
        }

class TaskTemplate(Base):
    """任务模板模型"""
    __tablename__ = "task_templates"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 基本信息
    name = Column(String(200), nullable=False, comment="模板名称")
    description = Column(Text, nullable=True, comment="模板描述")
    category = Column(String(100), nullable=True, comment="模板分类")
    
    # 模板配置
    template_config = Column(JSON, nullable=False, comment="模板配置")
    default_schedule = Column(JSON, nullable=True, comment="默认调度配置")
    
    # 元数据
    tags = Column(JSON, nullable=True, comment="标签")
    author = Column(String(200), nullable=True, comment="作者")
    version = Column(String(20), default="1.0.0", comment="模板版本")
    
    # 状态
    is_public = Column(Boolean, default=True, comment="是否公开")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    rating = Column(String(10), nullable=True, comment="评分")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<TaskTemplate(id={self.id}, name={self.name}, category={self.category})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "template_config": self.template_config,
            "default_schedule": self.default_schedule,
            "tags": self.tags,
            "author": self.author,
            "version": self.version,
            "is_public": self.is_public,
            "is_verified": self.is_verified,
            "usage_count": self.usage_count,
            "rating": self.rating,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
