# Mailer AI Agent Backend Dependencies
# FastAPI和Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库相关
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis和缓存
redis==5.0.1
celery==5.3.4

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# 配置管理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# 日志和监控
loguru==0.7.2
structlog==23.2.0

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 邮件处理
aiosmtplib==3.0.1
email-validator==2.1.0
imapclient==2.3.1

# AI和LLM集成
openai==1.3.7
anthropic==0.7.8
litellm==1.17.9

# MCP协议支持
websockets==12.0
mcp==0.1.0

# 文件处理
python-magic==0.4.27
PyPDF2==3.0.1
docling==1.0.0

# 数据验证和序列化
marshmallow==3.20.1
marshmallow-sqlalchemy==0.29.0

# 任务调度
apscheduler==3.10.4
croniter==2.0.1

# 工具和实用程序
click==8.1.7
rich==13.7.0
typer==0.9.0

# 测试相关
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 开发工具
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.1

# 生产环境
gunicorn==21.2.0
prometheus-client==0.19.0
