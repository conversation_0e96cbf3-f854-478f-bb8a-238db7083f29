"""
Mailer AI Agent Backend API
FastAPI主应用程序入口
"""

from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTP<PERSON>earer
from contextlib import asynccontextmanager
import uvicorn
import logging

# 导入路由模块
from api.auth import router as auth_router
from api.email import router as email_router
from api.agents import router as agents_router
from api.tasks import router as tasks_router
from api.mcp import router as mcp_router
from api.dashboard import router as dashboard_router

# 导入核心模块
from core.database import engine, Base, get_db
from core.config import settings
from core.logging_config import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 Mailer AI Agent API 启动中...")
    
    # 创建数据库表
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 数据库表创建成功")
    except Exception as e:
        logger.error(f"❌ 数据库表创建失败: {e}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("🛑 Mailer AI Agent API 关闭中...")

# 创建FastAPI应用实例
app = FastAPI(
    title="Mailer AI Agent API",
    description="通过邮件协同工作的通用AI Agent系统后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # 前端开发服务器
        "http://127.0.0.1:3000",
        "http://frontend:3000",   # Docker容器内通信
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(
    auth_router, 
    prefix="/api/auth", 
    tags=["🔐 认证管理"]
)

app.include_router(
    email_router, 
    prefix="/api/email", 
    tags=["📧 邮件管理"]
)

app.include_router(
    agents_router, 
    prefix="/api/agents", 
    tags=["🤖 Agent管理"]
)

app.include_router(
    tasks_router, 
    prefix="/api/tasks", 
    tags=["⚡ 任务管理"]
)

app.include_router(
    mcp_router, 
    prefix="/api/mcp", 
    tags=["🔌 MCP服务"]
)

app.include_router(
    dashboard_router, 
    prefix="/api/dashboard", 
    tags=["📊 仪表板"]
)

# 根路径
@app.get("/", tags=["系统信息"])
async def root():
    """API根路径，返回系统信息"""
    return {
        "message": "Mailer AI Agent API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc"
    }

# 健康检查
@app.get("/health", tags=["系统信息"])
async def health_check(db = Depends(get_db)):
    """健康检查接口，验证系统各组件状态"""
    try:
        # 检查数据库连接
        db.execute("SELECT 1")
        db_status = "healthy"
    except Exception as e:
        logger.error(f"数据库连接检查失败: {e}")
        db_status = "unhealthy"
        
    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "timestamp": "2024-12-28T00:00:00Z",
        "components": {
            "database": db_status,
            "api": "healthy"
        },
        "version": "1.0.0"
    }

# 系统信息
@app.get("/info", tags=["系统信息"])
async def system_info():
    """获取系统配置信息"""
    return {
        "app_name": "Mailer AI Agent",
        "version": "1.0.0",
        "environment": settings.environment,
        "database_url": settings.database_url.split("@")[-1] if "@" in settings.database_url else "配置中",
        "redis_url": settings.redis_url.split("@")[-1] if "@" in settings.redis_url else "配置中",
        "features": {
            "mcp_enabled": settings.mcp_enabled,
            "ai_providers": ["openai", "anthropic", "zhipu"],
            "email_protocols": ["imap", "pop3", "smtp"]
        }
    }

# 错误处理
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {
        "error": "Not Found",
        "message": f"路径 {request.url.path} 不存在",
        "status_code": 404
    }

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"内部服务器错误: {exc}")
    return {
        "error": "Internal Server Error",
        "message": "服务器内部错误，请稍后重试",
        "status_code": 500
    }

# 开发环境启动
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
