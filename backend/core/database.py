"""
数据库配置和连接管理
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
import logging

from .config import settings

logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,
    pool_recycle=300,
    # 对于SQLite，使用StaticPool
    poolclass=StaticPool if "sqlite" in settings.database_url else None,
    connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {}
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()

def get_db() -> Session:
    """
    获取数据库会话
    用于FastAPI依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()

@contextmanager
def get_db_context():
    """
    获取数据库会话上下文管理器
    用于非FastAPI环境
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        logger.error(f"数据库操作错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def init_db():
    """初始化数据库"""
    try:
        # 导入所有模型以确保它们被注册
        from models import user, email, agent, task, mcp
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 数据库表创建成功")
        
        # 创建初始数据
        create_initial_data()
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise

def create_initial_data():
    """创建初始数据"""
    with get_db_context() as db:
        try:
            # 检查是否已有数据
            from models.user import User
            existing_users = db.query(User).count()
            
            if existing_users == 0:
                # 创建默认管理员用户
                from models.user import User
                from core.security import get_password_hash
                
                admin_user = User(
                    email="<EMAIL>",
                    username="admin",
                    password_hash=get_password_hash("admin123"),
                    is_active=True,
                    is_superuser=True
                )
                db.add(admin_user)
                db.commit()
                logger.info("✅ 创建默认管理员用户成功")
                
                # 创建默认MCP服务
                from models.mcp import MCPService
                
                zhipu_mcp = MCPService(
                    name="智谱AI网络搜索",
                    description="智谱AI提供的网络搜索MCP服务",
                    server_url="https://open.bigmodel.cn/api/mcp/web_search/sse",
                    config={
                        "timeout": 30,
                        "max_retries": 3,
                        "retry_delay": 1.0
                    },
                    is_active=True
                )
                db.add(zhipu_mcp)
                db.commit()
                logger.info("✅ 创建默认MCP服务成功")
                
        except Exception as e:
            logger.error(f"❌ 创建初始数据失败: {e}")
            db.rollback()
            raise

def check_db_connection():
    """检查数据库连接"""
    try:
        with get_db_context() as db:
            db.execute("SELECT 1")
        logger.info("✅ 数据库连接正常")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

def reset_db():
    """重置数据库（仅用于开发环境）"""
    if settings.environment != "development":
        raise ValueError("只能在开发环境中重置数据库")
    
    try:
        # 删除所有表
        Base.metadata.drop_all(bind=engine)
        logger.info("✅ 数据库表删除成功")
        
        # 重新创建表
        init_db()
        logger.info("✅ 数据库重置成功")
        
    except Exception as e:
        logger.error(f"❌ 数据库重置失败: {e}")
        raise

# 数据库健康检查
async def health_check():
    """异步数据库健康检查"""
    try:
        with get_db_context() as db:
            result = db.execute("SELECT 1").fetchone()
            return {"status": "healthy", "result": result[0] if result else None}
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return {"status": "unhealthy", "error": str(e)}
