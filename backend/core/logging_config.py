"""
日志配置模块
统一管理应用程序的日志配置
"""

import logging
import logging.config
import sys
from pathlib import Path
from loguru import logger
import structlog

from .config import settings

def setup_logging():
    """设置应用程序日志配置"""
    
    # 创建日志目录
    log_dir = Path(settings.log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置loguru
    logger.remove()  # 移除默认处理器
    
    # 控制台输出
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 文件输出
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=settings.log_max_size,
        retention=settings.log_retention,
        compression="zip",
        encoding="utf-8"
    )
    
    # 错误日志单独文件
    error_log_file = log_dir / "error.log"
    logger.add(
        str(error_log_file),
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 配置标准库logging以使用loguru
    class InterceptHandler(logging.Handler):
        def emit(self, record):
            # 获取对应的loguru级别
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno
            
            # 查找调用者
            frame, depth = logging.currentframe(), 2
            while frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1
            
            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )
    
    # 设置标准库日志处理器
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # 配置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    # 配置structlog（用于结构化日志）
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

def get_logger(name: str):
    """获取指定名称的日志器"""
    return logger.bind(name=name)

def log_request(request, response_time: float = None):
    """记录HTTP请求日志"""
    log_data = {
        "method": request.method,
        "url": str(request.url),
        "user_agent": request.headers.get("user-agent"),
        "client_ip": request.client.host if request.client else None,
    }
    
    if response_time:
        log_data["response_time"] = f"{response_time:.3f}s"
    
    logger.info("HTTP请求", **log_data)

def log_error(error: Exception, context: dict = None):
    """记录错误日志"""
    log_data = {
        "error_type": type(error).__name__,
        "error_message": str(error),
    }
    
    if context:
        log_data.update(context)
    
    logger.error("应用程序错误", **log_data)

def log_email_processing(email_id: str, action: str, status: str, details: dict = None):
    """记录邮件处理日志"""
    log_data = {
        "email_id": email_id,
        "action": action,
        "status": status,
    }
    
    if details:
        log_data.update(details)
    
    logger.info("邮件处理", **log_data)

def log_agent_execution(agent_id: str, task_id: str, action: str, status: str, details: dict = None):
    """记录Agent执行日志"""
    log_data = {
        "agent_id": agent_id,
        "task_id": task_id,
        "action": action,
        "status": status,
    }
    
    if details:
        log_data.update(details)
    
    logger.info("Agent执行", **log_data)

def log_mcp_operation(service_name: str, operation: str, status: str, details: dict = None):
    """记录MCP操作日志"""
    log_data = {
        "service_name": service_name,
        "operation": operation,
        "status": status,
    }
    
    if details:
        log_data.update(details)
    
    logger.info("MCP操作", **log_data)
