"""
配置管理模块
管理应用程序的所有配置参数
"""

from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import Optional, List
import os
from pathlib import Path

class Settings(BaseSettings):
    """应用程序配置类"""
    
    # 基础配置
    app_name: str = Field("Mailer AI Agent", description="应用程序名称")
    version: str = Field("1.0.0", description="应用程序版本")
    environment: str = Field("development", description="运行环境")
    debug: bool = Field(False, description="调试模式")
    
    # 服务器配置
    host: str = Field("0.0.0.0", description="服务器主机")
    port: int = Field(8000, description="服务器端口")
    
    # 数据库配置
    database_url: str = Field(
        "postgresql://postgres:postgres@localhost:5432/mailer_ai_agent",
        description="数据库连接URL"
    )
    database_echo: bool = Field(False, description="数据库SQL日志")
    
    # Redis配置
    redis_url: str = Field(
        "redis://localhost:6379/0",
        description="Redis连接URL"
    )
    redis_password: Optional[str] = Field(None, description="Redis密码")
    
    # JWT认证配置
    secret_key: str = Field(
        "your-super-secret-key-change-in-production",
        description="JWT密钥"
    )
    algorithm: str = Field("HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(30, description="访问令牌过期时间(分钟)")
    refresh_token_expire_days: int = Field(7, description="刷新令牌过期时间(天)")
    
    # CORS配置
    allowed_origins: List[str] = Field(
        ["http://localhost:3000", "http://127.0.0.1:3000"],
        description="允许的跨域源"
    )
    
    # AI服务配置
    default_ai_provider: str = Field("openai", description="默认AI提供商")
    
    # OpenAI配置
    openai_api_key: Optional[str] = Field(None, description="OpenAI API密钥")
    openai_base_url: Optional[str] = Field(None, description="OpenAI API基础URL")
    openai_model: str = Field("gpt-3.5-turbo", description="默认OpenAI模型")
    
    # Anthropic配置
    anthropic_api_key: Optional[str] = Field(None, description="Anthropic API密钥")
    anthropic_model: str = Field("claude-3-sonnet-20240229", description="默认Anthropic模型")
    
    # 智谱AI配置
    zhipu_api_key: Optional[str] = Field(None, description="智谱AI API密钥")
    zhipu_model: str = Field("glm-4", description="默认智谱AI模型")
    
    # MCP配置
    mcp_enabled: bool = Field(True, description="是否启用MCP")
    mcp_server_url: str = Field(
        "https://open.bigmodel.cn/api/mcp/web_search/sse",
        description="MCP服务器URL"
    )
    mcp_timeout: int = Field(30, description="MCP连接超时时间")
    mcp_max_retries: int = Field(3, description="MCP最大重试次数")
    mcp_retry_delay: float = Field(1.0, description="MCP重试延迟")
    
    # 邮件配置
    default_email_provider: str = Field("gmail", description="默认邮件提供商")
    email_check_interval: int = Field(300, description="邮件检查间隔(秒)")
    max_emails_per_check: int = Field(50, description="每次检查最大邮件数")
    
    # 任务队列配置
    celery_broker_url: Optional[str] = Field(None, description="Celery代理URL")
    celery_result_backend: Optional[str] = Field(None, description="Celery结果后端")
    
    # 文件存储配置
    upload_dir: str = Field("uploads", description="上传文件目录")
    max_file_size: int = Field(10 * 1024 * 1024, description="最大文件大小(字节)")
    allowed_file_types: List[str] = Field(
        [".pdf", ".txt", ".doc", ".docx", ".xls", ".xlsx"],
        description="允许的文件类型"
    )
    
    # 日志配置
    log_level: str = Field("INFO", description="日志级别")
    log_file: str = Field("logs/backend.log", description="日志文件路径")
    log_max_size: str = Field("10 MB", description="日志文件最大大小")
    log_retention: str = Field("30 days", description="日志保留时间")
    
    # Agent运行时配置
    agent_runtime_url: str = Field(
        "http://agent-runtime:8001",
        description="Agent运行时服务URL"
    )
    agent_timeout: int = Field(300, description="Agent执行超时时间(秒)")
    max_concurrent_agents: int = Field(5, description="最大并发Agent数量")
    
    # 安全配置
    enable_rate_limiting: bool = Field(True, description="是否启用速率限制")
    rate_limit_requests: int = Field(100, description="速率限制请求数")
    rate_limit_window: int = Field(60, description="速率限制时间窗口(秒)")
    
    @validator("celery_broker_url", pre=True, always=True)
    def set_celery_broker_url(cls, v, values):
        """设置Celery代理URL"""
        if v is None:
            return values.get("redis_url", "redis://localhost:6379/0")
        return v
    
    @validator("celery_result_backend", pre=True, always=True)
    def set_celery_result_backend(cls, v, values):
        """设置Celery结果后端"""
        if v is None:
            return values.get("redis_url", "redis://localhost:6379/0")
        return v
    
    @validator("upload_dir", pre=True, always=True)
    def create_upload_dir(cls, v):
        """创建上传目录"""
        upload_path = Path(v)
        upload_path.mkdir(parents=True, exist_ok=True)
        return str(upload_path)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "allow"

# 创建全局配置实例
settings = Settings()

# 配置验证函数
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查必需的AI API密钥
    if not any([
        settings.openai_api_key,
        settings.anthropic_api_key,
        settings.zhipu_api_key
    ]):
        errors.append("至少需要配置一个AI提供商的API密钥")
    
    # 检查数据库URL
    if not settings.database_url:
        errors.append("数据库URL不能为空")
    
    # 检查JWT密钥
    if settings.secret_key == "your-super-secret-key-change-in-production":
        if settings.environment == "production":
            errors.append("生产环境必须更改默认的JWT密钥")
    
    if errors:
        raise ValueError(f"配置验证失败: {'; '.join(errors)}")
    
    return True

# 获取AI提供商配置
def get_ai_provider_config(provider: str) -> dict:
    """获取指定AI提供商的配置"""
    configs = {
        "openai": {
            "api_key": settings.openai_api_key,
            "base_url": settings.openai_base_url,
            "model": settings.openai_model
        },
        "anthropic": {
            "api_key": settings.anthropic_api_key,
            "model": settings.anthropic_model
        },
        "zhipu": {
            "api_key": settings.zhipu_api_key,
            "model": settings.zhipu_model
        }
    }
    
    return configs.get(provider, {})
