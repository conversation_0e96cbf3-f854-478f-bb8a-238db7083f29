#!/usr/bin/env python3
"""
基础搜索功能测试脚本
测试模拟搜索和基本功能
"""

import os
import sys
import json
from typing import Dict, Any

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from loguru import logger
from src.processors.ai_agent_components.components.search_handler import SearchHandler
from src.processors.ai_agent_components.models.search_models import SearchType


def test_mock_search_only():
    """测试纯模拟搜索功能"""
    logger.info("=== 测试纯模拟搜索功能 ===")
    
    # 创建搜索处理器（禁用所有真实搜索）
    config = {
        "max_results": 3,
        "timeout": 10,
        "language": "zh",
        "region": "CN",
        "mcp": {
            "enabled": False  # 禁用MCP
        },
        "fallback": {
            "enabled": False  # 禁用备用搜索
        }
    }
    
    search_handler = SearchHandler(config)
    logger.info(f"搜索模式: {search_handler.search_mode}")
    
    # 执行搜索
    query = "人工智能最新发展"
    search_context = search_handler.search(query, SearchType.GENERAL)
    
    # 显示结果
    logger.info(f"搜索查询: {query}")
    logger.info(f"搜索结果数: {search_context.total_results}")
    logger.info(f"搜索时间: {search_context.search_time:.3f}秒")
    logger.info(f"错误信息: {search_context.error_message}")
    
    if search_context.results:
        logger.info("搜索结果:")
        for i, result in enumerate(search_context.results, 1):
            logger.info(f"{i}. {result.title}")
            logger.info(f"   URL: {result.url}")
            logger.info(f"   摘要: {result.snippet[:100]}...")
            logger.info(f"   相关性: {result.relevance_score:.2f}")
            logger.info(f"   来源: {result.source}")
    else:
        logger.warning("没有找到搜索结果")
    
    return search_context


def test_search_quality_assessment():
    """测试搜索质量评估功能"""
    logger.info("\n=== 测试搜索质量评估功能 ===")
    
    config = {
        "max_results": 5,
        "quality_weights": {
            "title_relevance": 0.4,
            "snippet_relevance": 0.3,
            "source_authority": 0.2,
            "freshness": 0.1
        },
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    
    # 测试多个查询
    queries = [
        "深度学习算法",
        "区块链技术应用",
        "云计算服务"
    ]
    
    for query in queries:
        logger.info(f"\n测试查询: {query}")
        search_context = search_handler.search(query)
        
        if search_context.results:
            # 显示相关性评分
            logger.info("相关性评分:")
            for i, result in enumerate(search_context.results, 1):
                logger.info(f"{i}. {result.title} (评分: {result.relevance_score:.3f})")
        
        # 显示摘要
        summary = search_context.get_summary()
        logger.info(f"搜索摘要:\n{summary[:200]}...")


def test_different_search_types():
    """测试不同搜索类型"""
    logger.info("\n=== 测试不同搜索类型 ===")
    
    config = {
        "max_results": 2,
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    
    test_cases = [
        ("通用搜索", "最新科技新闻", SearchType.GENERAL),
        ("技术搜索", "Python异步编程", SearchType.TECHNICAL),
        ("学术搜索", "量子计算研究", SearchType.ACADEMIC),
        ("新闻搜索", "今日头条新闻", SearchType.NEWS),
        ("产品搜索", "智能手机推荐", SearchType.PRODUCT)
    ]
    
    for test_name, query, search_type in test_cases:
        logger.info(f"\n{test_name}: {query}")
        search_context = search_handler.search(query, search_type)
        
        logger.info(f"结果数: {search_context.total_results}")
        if search_context.results:
            for result in search_context.results:
                logger.info(f"- {result.title} (评分: {result.relevance_score:.2f})")


def test_search_context_methods():
    """测试搜索上下文方法"""
    logger.info("\n=== 测试搜索上下文方法 ===")
    
    config = {
        "max_results": 5,
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    search_context = search_handler.search("机器学习教程")
    
    if search_context.results:
        # 测试 get_top_results
        top_3 = search_context.get_top_results(3)
        logger.info(f"前3个最相关结果:")
        for i, result in enumerate(top_3, 1):
            logger.info(f"{i}. {result.title} (评分: {result.relevance_score:.3f})")
        
        # 测试 get_summary
        summary = search_context.get_summary()
        logger.info(f"\n搜索摘要:\n{summary}")
        
        # 测试 to_dict
        context_dict = search_context.to_dict()
        logger.info(f"\n上下文字典键: {list(context_dict.keys())}")
        logger.info(f"请求信息: {context_dict['request']}")


def test_multiple_queries():
    """测试多查询搜索"""
    logger.info("\n=== 测试多查询搜索 ===")
    
    config = {
        "max_results": 2,
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    
    # 执行多个搜索查询
    queries = ["人工智能", "机器学习", "深度学习"]
    search_contexts = search_handler.search_multiple_queries(queries, SearchType.TECHNICAL)
    
    for i, context in enumerate(search_contexts):
        logger.info(f"\n查询 {i+1}: {context.request.query}")
        logger.info(f"结果数: {context.total_results}")
        logger.info(f"搜索时间: {context.search_time:.3f}秒")


def test_error_handling():
    """测试错误处理"""
    logger.info("\n=== 测试错误处理 ===")
    
    config = {
        "max_results": 3,
        "timeout": 1,  # 很短的超时时间
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    
    try:
        # 测试空查询
        search_context = search_handler.search("")
        logger.info(f"空查询结果数: {search_context.total_results}")
        
        # 测试特殊字符查询
        search_context = search_handler.search("!@#$%^&*()")
        logger.info(f"特殊字符查询结果数: {search_context.total_results}")
        
        # 测试很长的查询
        long_query = "这是一个非常长的查询" * 20
        search_context = search_handler.search(long_query)
        logger.info(f"长查询结果数: {search_context.total_results}")
        
    except Exception as e:
        logger.error(f"错误处理测试中出现异常: {e}")


def main():
    """主测试函数"""
    logger.info("开始测试基础搜索功能")
    
    # 测试纯模拟搜索
    mock_result = test_mock_search_only()
    
    # 测试搜索质量评估
    test_search_quality_assessment()
    
    # 测试不同搜索类型
    test_different_search_types()
    
    # 测试搜索上下文方法
    test_search_context_methods()
    
    # 测试多查询搜索
    test_multiple_queries()
    
    # 测试错误处理
    test_error_handling()
    
    # 总结
    logger.info("\n=== 测试总结 ===")
    logger.info(f"模拟搜索: {'成功' if mock_result and mock_result.results else '失败'}")
    
    if mock_result and mock_result.results:
        logger.info("✅ 基础搜索功能正常工作")
        logger.info("✅ 搜索结果生成正常")
        logger.info("✅ 相关性评分功能正常")
        logger.info("✅ 搜索上下文方法正常")
    else:
        logger.error("❌ 基础搜索功能存在问题")
    
    logger.info("\n基础搜索功能测试完成！")


if __name__ == "__main__":
    main()
