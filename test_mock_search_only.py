#!/usr/bin/env python3
"""
纯模拟搜索测试脚本
专门测试模拟搜索功能，不依赖网络
"""

import os
import sys
import json
from typing import Dict, Any

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from loguru import logger
from src.processors.ai_agent_components.components.search_handler import SearchHandler
from src.processors.ai_agent_components.models.search_models import SearchType


def test_pure_mock_search():
    """测试纯模拟搜索功能"""
    logger.info("=== 测试纯模拟搜索功能 ===")
    
    # 创建搜索处理器（完全禁用真实搜索）
    config = {
        "max_results": 3,
        "timeout": 10,
        "language": "zh",
        "region": "CN",
        "mcp": {
            "enabled": False  # 明确禁用MCP
        },
        "fallback": {
            "enabled": False  # 明确禁用备用搜索
        }
    }
    
    search_handler = SearchHandler(config)
    logger.info(f"搜索模式: {search_handler.search_mode}")
    
    # 确保是模拟模式
    if search_handler.search_mode != "mock":
        logger.error(f"期望模拟模式，但得到: {search_handler.search_mode}")
        return None
    
    # 执行搜索
    query = "人工智能最新发展"
    search_context = search_handler.search(query, SearchType.GENERAL)
    
    # 显示结果
    logger.info(f"搜索查询: {query}")
    logger.info(f"搜索结果数: {search_context.total_results}")
    logger.info(f"搜索时间: {search_context.search_time:.3f}秒")
    logger.info(f"错误信息: {search_context.error_message}")
    
    if search_context.results:
        logger.info("搜索结果:")
        for i, result in enumerate(search_context.results, 1):
            logger.info(f"{i}. {result.title}")
            logger.info(f"   URL: {result.url}")
            logger.info(f"   摘要: {result.snippet[:100]}...")
            logger.info(f"   相关性: {result.relevance_score:.2f}")
            logger.info(f"   来源: {result.source}")
        
        logger.info("✅ 模拟搜索功能正常")
        return search_context
    else:
        logger.error("❌ 模拟搜索没有返回结果")
        return None


def test_different_queries():
    """测试不同查询的模拟搜索"""
    logger.info("\n=== 测试不同查询的模拟搜索 ===")
    
    config = {
        "max_results": 2,
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    
    queries = [
        "机器学习算法",
        "深度学习框架",
        "自然语言处理",
        "计算机视觉",
        "强化学习"
    ]
    
    all_success = True
    
    for query in queries:
        logger.info(f"\n测试查询: {query}")
        search_context = search_handler.search(query)
        
        if search_context.results:
            logger.info(f"✅ 成功: {len(search_context.results)} 个结果")
            for result in search_context.results:
                logger.info(f"  - {result.title} (评分: {result.relevance_score:.2f})")
        else:
            logger.error(f"❌ 失败: 没有结果")
            all_success = False
    
    return all_success


def test_search_types():
    """测试不同搜索类型"""
    logger.info("\n=== 测试不同搜索类型 ===")
    
    config = {
        "max_results": 2,
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    
    test_cases = [
        ("通用搜索", "科技新闻", SearchType.GENERAL),
        ("技术搜索", "Python编程", SearchType.TECHNICAL),
        ("学术搜索", "量子计算", SearchType.ACADEMIC),
        ("新闻搜索", "今日新闻", SearchType.NEWS),
        ("产品搜索", "手机推荐", SearchType.PRODUCT)
    ]
    
    all_success = True
    
    for test_name, query, search_type in test_cases:
        logger.info(f"\n{test_name}: {query}")
        search_context = search_handler.search(query, search_type)
        
        if search_context.results:
            logger.info(f"✅ 成功: {len(search_context.results)} 个结果")
        else:
            logger.error(f"❌ 失败: 没有结果")
            all_success = False
    
    return all_success


def test_search_context_features():
    """测试搜索上下文功能"""
    logger.info("\n=== 测试搜索上下文功能 ===")
    
    config = {
        "max_results": 5,
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    search_context = search_handler.search("人工智能技术")
    
    if not search_context.results:
        logger.error("❌ 没有搜索结果，无法测试上下文功能")
        return False
    
    try:
        # 测试 get_top_results
        top_3 = search_context.get_top_results(3)
        logger.info(f"✅ get_top_results: 获取到 {len(top_3)} 个结果")
        
        # 测试 get_summary
        summary = search_context.get_summary()
        logger.info(f"✅ get_summary: 生成摘要长度 {len(summary)} 字符")
        
        # 测试 to_dict
        context_dict = search_context.to_dict()
        logger.info(f"✅ to_dict: 包含 {len(context_dict)} 个键")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 上下文功能测试失败: {e}")
        return False


def test_quality_assessment():
    """测试搜索质量评估"""
    logger.info("\n=== 测试搜索质量评估 ===")
    
    config = {
        "max_results": 3,
        "quality_weights": {
            "title_relevance": 0.4,
            "snippet_relevance": 0.3,
            "source_authority": 0.2,
            "freshness": 0.1
        },
        "mcp": {"enabled": False},
        "fallback": {"enabled": False}
    }
    
    search_handler = SearchHandler(config)
    search_context = search_handler.search("机器学习教程")
    
    if not search_context.results:
        logger.error("❌ 没有搜索结果，无法测试质量评估")
        return False
    
    try:
        # 检查相关性评分
        for i, result in enumerate(search_context.results, 1):
            score = result.relevance_score
            if 0 <= score <= 1:
                logger.info(f"✅ 结果 {i}: 相关性评分 {score:.3f} (有效范围)")
            else:
                logger.error(f"❌ 结果 {i}: 相关性评分 {score:.3f} (超出范围)")
                return False
        
        # 检查结果排序
        scores = [result.relevance_score for result in search_context.results]
        if scores == sorted(scores, reverse=True):
            logger.info("✅ 搜索结果按相关性正确排序")
        else:
            logger.error("❌ 搜索结果排序不正确")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 质量评估测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始测试纯模拟搜索功能")
    
    # 测试基本模拟搜索
    basic_result = test_pure_mock_search()
    
    # 测试不同查询
    queries_result = test_different_queries()
    
    # 测试不同搜索类型
    types_result = test_search_types()
    
    # 测试搜索上下文功能
    context_result = test_search_context_features()
    
    # 测试质量评估
    quality_result = test_quality_assessment()
    
    # 总结
    logger.info("\n=== 测试总结 ===")
    logger.info(f"基本模拟搜索: {'✅ 成功' if basic_result else '❌ 失败'}")
    logger.info(f"不同查询测试: {'✅ 成功' if queries_result else '❌ 失败'}")
    logger.info(f"搜索类型测试: {'✅ 成功' if types_result else '❌ 失败'}")
    logger.info(f"上下文功能测试: {'✅ 成功' if context_result else '❌ 失败'}")
    logger.info(f"质量评估测试: {'✅ 成功' if quality_result else '❌ 失败'}")
    
    all_passed = all([basic_result, queries_result, types_result, context_result, quality_result])
    
    if all_passed:
        logger.info("\n🎉 所有模拟搜索测试通过！")
        logger.info("模拟搜索功能完全正常，可以作为MCP和备用搜索的可靠后备方案。")
    else:
        logger.error("\n❌ 部分测试失败，需要检查模拟搜索实现。")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
