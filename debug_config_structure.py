#!/usr/bin/env python3
"""
调试配置结构
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.core.config_manager import ConfigManager

def debug_config_structure():
    """调试配置结构"""
    logger.info("开始调试配置结构...")
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        logger.info(f"配置类型: {type(config)}")
        logger.info(f"配置属性: {dir(config)}")
        
        if hasattr(config, 'processors'):
            logger.info(f"processors类型: {type(config.processors)}")
            logger.info(f"processors属性: {dir(config.processors)}")
            
            if hasattr(config.processors, 'ai_reply_processor'):
                logger.info(f"ai_reply_processor类型: {type(config.processors.ai_reply_processor)}")
                logger.info(f"ai_reply_processor属性: {dir(config.processors.ai_reply_processor)}")
                
                if hasattr(config.processors.ai_reply_processor, 'search'):
                    logger.info(f"search配置: {config.processors.ai_reply_processor.search}")
                else:
                    logger.warning("ai_reply_processor没有search属性")
            else:
                logger.warning("processors没有ai_reply_processor属性")
        else:
            logger.warning("config没有processors属性")
            
        # 尝试直接访问
        if isinstance(config, dict):
            logger.info("配置是字典类型")
            logger.info(f"顶级键: {list(config.keys())}")
            
            if 'processors' in config:
                logger.info(f"processors键: {list(config['processors'].keys())}")

                if 'ai_reply_processor' in config['processors']:
                    ai_reply_config = config['processors']['ai_reply_processor']
                    logger.info(f"ai_reply_processor配置键: {list(ai_reply_config.keys())}")
                    logger.info(f"ai_reply_processor完整配置: {ai_reply_config}")

                    if 'search' in ai_reply_config:
                        search_config = ai_reply_config['search']
                        logger.info(f"search配置: {search_config}")
                    else:
                        logger.warning("ai_reply_processor没有search配置")
                else:
                    logger.warning("processors没有ai_reply_processor")
            else:
                logger.warning("config没有processors键")
        
        logger.success("配置结构调试完成!")
        
    except Exception as e:
        logger.error(f"配置结构调试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行调试
    debug_config_structure()
