{"name": "mailer-ai-agent-frontend", "version": "1.0.0", "description": "Mailer AI Agent 系统前端界面", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist"}, "dependencies": {"next": "14.0.4", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "5.3.3", "@types/node": "20.10.5", "@types/react": "18.2.45", "@types/react-dom": "18.2.18", "tailwindcss": "3.3.6", "autoprefixer": "10.4.16", "postcss": "8.4.32", "@tailwindcss/forms": "0.5.7", "@tailwindcss/typography": "0.5.10", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-sheet": "^1.0.4", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-table": "^1.0.4", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.294.0", "zustand": "^4.4.7", "@tanstack/react-query": "^5.14.2", "@tanstack/react-query-devtools": "^5.14.2", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^3.0.6", "react-day-picker": "^8.10.0", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-syntax-highlighter": "^15.5.0", "socket.io-client": "^4.7.4", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "react-intersection-observer": "^9.5.3", "next-themes": "^0.2.1", "next-auth": "^4.24.5", "@next/bundle-analyzer": "^14.0.4"}, "devDependencies": {"eslint": "8.56.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.11", "@types/react-syntax-highlighter": "^15.5.11", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check"}}}