'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  EnvelopeIcon, 
  CpuChipIcon, 
  ClockIcon, 
  ServerIcon,
  ChartBarIcon,
  BellIcon
} from '@heroicons/react/24/outline'

interface SystemStats {
  email: {
    total: number
    unread: number
    today: number
  }
  agents: {
    total: number
    active: number
    executions_today: number
  }
  tasks: {
    total: number
    active: number
    scheduled: number
  }
  mcp_services: {
    total: number
    active: number
    calls_today: number
  }
}

export default function HomePage() {
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 模拟API调用
    setTimeout(() => {
      setStats({
        email: {
          total: 1247,
          unread: 23,
          today: 15
        },
        agents: {
          total: 8,
          active: 5,
          executions_today: 42
        },
        tasks: {
          total: 12,
          active: 7,
          scheduled: 4
        },
        mcp_services: {
          total: 6,
          active: 4,
          calls_today: 156
        }
      })
      setLoading(false)
    }, 1000)
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                Mailer AI Agent
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-500">
                <BellIcon className="h-6 w-6" />
              </button>
              <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">U</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* 欢迎区域 */}
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900">系统概览</h2>
            <p className="mt-1 text-sm text-gray-600">
              欢迎使用Mailer AI Agent系统，这里是您的智能邮件处理中心
            </p>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            {/* 邮件统计 */}
            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <EnvelopeIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      邮件管理
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stats?.email.total}
                      </div>
                      <div className="ml-2 flex items-baseline text-sm">
                        <span className="text-red-600 font-medium">
                          {stats?.email.unread} 未读
                        </span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/emails" className="text-sm text-blue-600 hover:text-blue-500">
                  查看邮件 →
                </Link>
              </div>
            </div>

            {/* Agent统计 */}
            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CpuChipIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      AI Agents
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stats?.agents.total}
                      </div>
                      <div className="ml-2 flex items-baseline text-sm">
                        <span className="text-green-600 font-medium">
                          {stats?.agents.active} 活跃
                        </span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/agents" className="text-sm text-blue-600 hover:text-blue-500">
                  管理Agents →
                </Link>
              </div>
            </div>

            {/* 任务统计 */}
            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      任务调度
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stats?.tasks.total}
                      </div>
                      <div className="ml-2 flex items-baseline text-sm">
                        <span className="text-yellow-600 font-medium">
                          {stats?.tasks.scheduled} 调度中
                        </span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/tasks" className="text-sm text-blue-600 hover:text-blue-500">
                  查看任务 →
                </Link>
              </div>
            </div>

            {/* MCP服务统计 */}
            <div className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ServerIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      MCP服务
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stats?.mcp_services.total}
                      </div>
                      <div className="ml-2 flex items-baseline text-sm">
                        <span className="text-purple-600 font-medium">
                          {stats?.mcp_services.active} 在线
                        </span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/mcp" className="text-sm text-blue-600 hover:text-blue-500">
                  管理服务 →
                </Link>
              </div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className="mt-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <Link href="/emails/compose" className="card hover:shadow-lg transition-shadow">
                <div className="flex items-center">
                  <EnvelopeIcon className="h-6 w-6 text-blue-600" />
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    撰写邮件
                  </span>
                </div>
              </Link>
              
              <Link href="/agents/create" className="card hover:shadow-lg transition-shadow">
                <div className="flex items-center">
                  <CpuChipIcon className="h-6 w-6 text-green-600" />
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    创建Agent
                  </span>
                </div>
              </Link>
              
              <Link href="/dashboard" className="card hover:shadow-lg transition-shadow">
                <div className="flex items-center">
                  <ChartBarIcon className="h-6 w-6 text-purple-600" />
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    查看仪表板
                  </span>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
