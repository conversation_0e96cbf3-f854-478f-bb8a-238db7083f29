# Mailer AI Agent Frontend Dockerfile
# 多阶段构建，支持开发和生产环境

# 基础镜像
FROM node:18-alpine as base

# 设置环境变量
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    NPM_CONFIG_CACHE=/tmp/.npm

# 安装系统依赖
RUN apk add --no-cache \
    libc6-compat \
    curl

# 创建应用用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 设置工作目录
WORKDIR /app

# 依赖安装阶段
FROM base as deps

# 复制包管理文件
COPY package.json package-lock.json* ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 开发依赖安装阶段
FROM base as dev-deps

# 复制包管理文件
COPY package.json package-lock.json* ./

# 安装所有依赖（包括开发依赖）
RUN npm ci && npm cache clean --force

# 构建阶段
FROM dev-deps as builder

# 复制源代码
COPY . .

# 设置构建环境变量
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_WS_URL
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_APP_VERSION

ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL}
ENV NEXT_PUBLIC_APP_NAME=${NEXT_PUBLIC_APP_NAME}
ENV NEXT_PUBLIC_APP_VERSION=${NEXT_PUBLIC_APP_VERSION}

# 构建应用
RUN npm run build

# 开发环境阶段
FROM dev-deps as development

# 复制源代码
COPY . .

# 切换到应用用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# 启动开发服务器
CMD ["npm", "run", "dev"]

# 生产环境阶段
FROM base as production

# 复制生产依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制构建产物
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# 设置正确的权限
RUN chown -R nextjs:nodejs /app

# 切换到应用用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# 启动生产服务器
CMD ["node", "server.js"]

# 测试阶段
FROM dev-deps as test

# 复制源代码
COPY . .

# 运行测试
RUN npm run test && \
    npm run lint && \
    npm run type-check

# 构建验证
RUN npm run build
