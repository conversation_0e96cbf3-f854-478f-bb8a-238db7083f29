import pytest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from imaplib import IMAP4_SSL
from email.message import Message
from src.core.email_client import EmailClient, EmailMessageWrapper, EmailProcessingError, EmailConfig
from src.core.config_manager import EmailConfig


@pytest.fixture
def mock_email_config():
    """创建测试用的邮件配置"""
    return EmailConfig(
        host="imap.test.com",
        port=993,
        username="<EMAIL>",
        password="password",
        protocol="imap",
        use_ssl=True,
        smtp=None  # smtp 配置是可选的
    )


@pytest.fixture
def mock_imap():
    """模拟IMAP连接"""
    with patch('imaplib.IMAP4_SSL') as mock:
        mock_conn = MagicMock(spec=IMAP4_SSL)
        mock.return_value = mock_conn
        yield mock_conn


@pytest.fixture
def client(mock_email_config):
    """创建邮件客户端实例"""
    return EmailClient(mock_email_config)


def test_email_client_connection(client, mock_imap):
    """测试邮件客户端连接"""
    # 配置模拟行为
    mock_imap.login.return_value = ('OK', [b'Logged in'])
    mock_imap.select.return_value = ('OK', [b'1'])

    # 测试连接
    assert client.connect() is True
    mock_imap.login.assert_called_once()


def test_mark_as_read_with_network_failure(client, mock_imap):
    """测试网络中断时的邮件标记行为"""
    # 设置连接并模拟网络错误
    client.connect()
    mock_imap.uid.side_effect = ConnectionError("Network failure")

    # 测试标记邮件
    success = client.mark_as_read("123", "INBOX")
    assert success is False

    # 验证重试行为
    assert mock_imap.uid.call_count >= 1
    

def test_attachment_download_timeout(client, mock_imap):
    """测试附件下载超时"""
    # 配置模拟超时行为
    def timeout_effect(*args, **kwargs):
        time.sleep(0.1)
        raise TimeoutError("Download timeout")

    mock_imap.uid.side_effect = timeout_effect
    client.connect()

    # 测试获取邮件
    emails = client.fetch_emails(limit=1)
    assert len(emails) == 0  # 由于超时应该返回空列表


def test_concurrent_polling(client, mock_imap):
    """测试并发轮询时的邮箱锁机制"""
    processed_emails = []
    
    def mock_callback(email):
        processed_emails.append(email)
        time.sleep(0.1)  # 模拟处理时间
    
    # 启动轮询
    client.start_polling(callback=mock_callback)
    
    # 尝试同时启动另一个轮询
    client.start_polling(callback=mock_callback)
    
    # 应该只有一个轮询线程在运行
    time.sleep(0.2)
    assert len([t for t in threading.enumerate() if t.name.startswith('Thread-')]) == 1
    
    # 停止轮询
    client.stop_polling()


def test_email_processing_stats(client, mock_imap):
    """测试邮件处理统计"""
    # 模拟一些邮件处理
    mock_message = MagicMock(spec=Message)
    mock_message.get.return_value = ""
    
    mock_imap.uid.return_value = ('OK', [(b'1 (UID 123)', mock_message)])
    mock_imap.select.return_value = ('OK', [b'1'])
    
    def process_email(email):
        if email.uid == "123":
            raise Exception("Processing error")
    
    # 启动轮询
    client.start_polling(callback=process_email)
    time.sleep(0.2)  # 等待处理完成
    client.stop_polling()
    
    # 验证统计
    assert client.stats.total_processed >= 0
    assert client.stats.failed >= 0
    assert isinstance(client.stats.success_rate, float)


def test_error_logging(client, mock_imap, tmp_path):
    """测试错误日志记录"""
    # 设置临时日志文件
    log_file = tmp_path / "email_errors.log"
    client._error_log_path = str(log_file)
    
    # 模拟错误
    error_info = {
        "email_uid": "123",
        "folder": "INBOX",
        "error": "Test error",
        "subject": "Test Subject"
    }
    client._log_error(error_info)
    
    # 验证日志文件
    assert log_file.exists()
    content = log_file.read_text()
    assert "Test error" in content
    assert "123" in content


def test_email_wrapper_methods():
    """测试邮件包装器方法"""
    # 创建模拟邮件消息
    mock_message = MagicMock(spec=Message)
    mock_message.get.return_value = "test subject"
    mock_message.is_multipart.return_value = False
    mock_message.get_payload.return_value = "test body"
    
    # 测试包装器
    wrapper = EmailMessageWrapper(mock_message)
    assert wrapper.subject == "test subject"
    assert len(wrapper.attachments) == 0
    assert isinstance(wrapper.to_dict(), dict)


def test_transaction_rollback(client, mock_imap):
    """测试事务回滚机制"""
    # 设置模拟行为
    client.connect()
    mock_imap.uid.side_effect = [
        ('OK', [b'1']),  # 检查邮件存在
        Exception("Update failed"),  # 标记失败
        ('OK', [b'1']),  # 回滚成功
    ]
    
    # 测试标记邮件
    success = client.mark_as_read("123", "INBOX")
    assert not success
    
    # 验证回滚操作
    calls = mock_imap.uid.call_args_list
    assert len(calls) >= 3  # 检查、更新、回滚


def test_polling_interval_respect(client, mock_imap):
    """测试轮询间隔遵守"""
    processed_times = []
    
    def mock_callback(email):
        processed_times.append(time.time())
    
    # 设置较短的轮询间隔用于测试
    original_interval = client.POLLING_INTERVAL
    client.POLLING_INTERVAL = 0.1
    
    try:
        client.start_polling(callback=mock_callback)
        time.sleep(0.3)  # 等待多次轮询
        client.stop_polling()
        
        # 检查轮询间隔
        if len(processed_times) >= 2:
            intervals = [processed_times[i+1] - processed_times[i] 
                       for i in range(len(processed_times)-1)]
            for interval in intervals:
                assert interval >= 0.1  # 确保遵守最小间隔
    finally:
        client.POLLING_INTERVAL = original_interval
