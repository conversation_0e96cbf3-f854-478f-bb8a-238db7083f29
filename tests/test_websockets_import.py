#!/usr/bin/env python3
"""
测试websockets库的导入和基本功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_websockets_import():
    """测试websockets库导入"""
    try:
        import websockets
        print(f"✅ websockets库导入成功，版本: {websockets.__version__}")
        return True
    except ImportError as e:
        print(f"❌ websockets库导入失败: {e}")
        print("请运行: pip install websockets")
        return False

def test_httpx_import():
    """测试httpx库导入"""
    try:
        import httpx
        print(f"✅ httpx库导入成功，版本: {httpx.__version__}")
        return True
    except ImportError as e:
        print(f"❌ httpx库导入失败: {e}")
        print("请运行: pip install httpx")
        return False

def test_mcp_client_import():
    """测试MCP客户端导入"""
    try:
        from processors.ai_agent_components.components.mcp_client import (
            MCPClient,
            FallbackWebSearcher,
            ASYNC_LIBS_AVAILABLE
        )
        print(f"✅ MCP客户端导入成功")
        print(f"   异步库可用: {ASYNC_LIBS_AVAILABLE}")
        return True
    except ImportError as e:
        print(f"❌ MCP客户端导入失败: {e}")
        return False

def test_mcp_client_initialization():
    """测试MCP客户端初始化"""
    try:
        from processors.ai_agent_components.components.mcp_client import MCPClient
        
        config = {
            'enabled': True,
            'server_url': 'ws://localhost:8080',
            'timeout': 30
        }
        
        client = MCPClient(config)
        print(f"✅ MCP客户端初始化成功")
        print(f"   服务器URL: {client.server_url}")
        print(f"   超时时间: {client.timeout}")
        print(f"   连接状态: {client.connection_state}")
        return True
    except Exception as e:
        print(f"❌ MCP客户端初始化失败: {e}")
        return False

def test_search_handler_import():
    """测试搜索处理器导入"""
    try:
        from processors.ai_agent_components.components.search_handler import SearchHandler
        
        config = {
            'mcp': {
                'enabled': True,
                'server_url': 'ws://localhost:8080'
            }
        }
        
        handler = SearchHandler(config)
        print(f"✅ 搜索处理器初始化成功")
        print(f"   搜索模式: {handler.search_mode}")
        
        # 获取搜索统计信息
        stats = handler.get_search_statistics()
        print(f"   MCP可用: {stats['mcp_available']}")
        print(f"   备用搜索可用: {stats['fallback_available']}")
        
        return True
    except Exception as e:
        print(f"❌ 搜索处理器初始化失败: {e}")
        return False

def test_async_functionality():
    """测试异步功能"""
    try:
        import asyncio
        from processors.ai_agent_components.components.mcp_client import MCPClient
        
        async def test_async():
            config = {
                'server_url': 'ws://localhost:8080',
                'timeout': 5  # 短超时用于测试
            }
            
            client = MCPClient(config)
            
            # 测试连接（预期会失败，因为没有真实的MCP服务器）
            try:
                result = await client.connect()
                print(f"   连接结果: {result}")
            except Exception as e:
                print(f"   连接失败（预期）: {type(e).__name__}")
            
            return True
        
        # 运行异步测试
        result = asyncio.run(test_async())
        print(f"✅ 异步功能测试完成")
        return result
        
    except Exception as e:
        print(f"❌ 异步功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试websockets和MCP客户端...")
    print("=" * 50)
    
    tests = [
        ("websockets库导入", test_websockets_import),
        ("httpx库导入", test_httpx_import),
        ("MCP客户端导入", test_mcp_client_import),
        ("MCP客户端初始化", test_mcp_client_initialization),
        ("搜索处理器导入", test_search_handler_import),
        ("异步功能", test_async_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   测试失败")
        except Exception as e:
            print(f"   测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP客户端可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查依赖库安装。")
        print("\n💡 修复建议:")
        print("   1. 确保安装了所需依赖: pip install -r requirements.txt")
        print("   2. 检查Python环境是否正确")
        print("   3. 验证websockets和httpx库版本")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
