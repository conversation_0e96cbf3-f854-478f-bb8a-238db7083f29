"""
MCP客户端单元测试
测试zhipu-web-search-sse MCP集成功能
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.processors.ai_agent_components.components.mcp_client import (
    MCPClient, 
    FallbackWebSearcher,
    MCPConnectionState,
    MCPRequest,
    MCPResponse
)
from src.processors.ai_agent_components.components.search_handler import SearchHandler
from src.processors.ai_agent_components.models.search_models import SearchType


class TestMCPClient(unittest.TestCase):
    """MCP客户端测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            'enabled': True,
            'server_url': 'ws://localhost:8080',
            'api_key': 'test_api_key',
            'timeout': 30,
            'max_retries': 3,
            'retry_delay': 1.0,
            'connection_pool_size': 5,
            'heartbeat_interval': 30,
            'zhipu_search': {
                'preferred_engines': ['google', 'bing'],
                'quality_settings': {
                    'relevance_threshold': 0.6
                }
            }
        }
        self.mcp_client = MCPClient(self.config)
    
    def test_mcp_client_initialization(self):
        """测试MCP客户端初始化"""
        self.assertEqual(self.mcp_client.server_url, 'ws://localhost:8080')
        self.assertEqual(self.mcp_client.api_key, 'test_api_key')
        self.assertEqual(self.mcp_client.timeout, 30)
        self.assertEqual(self.mcp_client.max_retries, 3)
        self.assertEqual(self.mcp_client.connection_state, MCPConnectionState.DISCONNECTED)
    
    def test_standardize_search_result(self):
        """测试搜索结果标准化"""
        raw_result = {
            'title': 'Test Title',
            'url': 'https://example.com',
            'snippet': 'Test snippet content',
            'source': 'Example.com',
            'publish_date': '2024-01-15'
        }
        
        standardized = self.mcp_client._standardize_search_result(raw_result)
        
        self.assertIsNotNone(standardized)
        self.assertEqual(standardized['title'], 'Test Title')
        self.assertEqual(standardized['url'], 'https://example.com')
        self.assertEqual(standardized['snippet'], 'Test snippet content')
        self.assertEqual(standardized['source'], 'Example.com')
        self.assertEqual(standardized['publish_date'], '2024-01-15')
    
    def test_standardize_search_result_missing_fields(self):
        """测试缺少必需字段的搜索结果"""
        raw_result = {
            'snippet': 'Test snippet content',
            'source': 'Example.com'
        }
        
        standardized = self.mcp_client._standardize_search_result(raw_result)
        self.assertIsNone(standardized)  # 缺少title和url，应该返回None
    
    def test_standardize_search_result_alternative_fields(self):
        """测试使用备用字段名的搜索结果"""
        raw_result = {
            'title': 'Test Title',
            'link': 'https://example.com',  # 使用link而不是url
            'description': 'Test description',  # 使用description而不是snippet
            'domain': 'Example Domain',  # 使用domain而不是source
            'date': '2024-01-15'  # 使用date而不是publish_date
        }
        
        standardized = self.mcp_client._standardize_search_result(raw_result)
        
        self.assertIsNotNone(standardized)
        self.assertEqual(standardized['title'], 'Test Title')
        self.assertEqual(standardized['url'], 'https://example.com')
        self.assertEqual(standardized['snippet'], 'Test description')
        self.assertEqual(standardized['source'], 'Example Domain')
        self.assertEqual(standardized['publish_date'], '2024-01-15')
    
    @patch('src.processors.ai_agent_components.components.mcp_client.ASYNC_LIBS_AVAILABLE', False)
    def test_search_web_sync_no_async_libs(self):
        """测试没有异步库时的同步搜索"""
        result = self.mcp_client.search_web_sync("test query")
        self.assertEqual(result, [])
    
    def test_mcp_request_creation(self):
        """测试MCP请求创建"""
        request = MCPRequest(
            id="test_id",
            method="search",
            params={"query": "test"}
        )
        
        self.assertEqual(request.id, "test_id")
        self.assertEqual(request.method, "search")
        self.assertEqual(request.params, {"query": "test"})
        self.assertEqual(request.retries, 0)
    
    def test_mcp_response_creation(self):
        """测试MCP响应创建"""
        response = MCPResponse(
            id="test_id",
            result={"results": []},
            error=None
        )
        
        self.assertEqual(response.id, "test_id")
        self.assertEqual(response.result, {"results": []})
        self.assertIsNone(response.error)


class TestFallbackWebSearcher(unittest.TestCase):
    """备用搜索器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            'timeout': 15,
            'user_agent': 'Test Bot'
        }
        self.searcher = FallbackWebSearcher(self.config)
    
    def test_fallback_searcher_initialization(self):
        """测试备用搜索器初始化"""
        self.assertEqual(self.searcher.timeout, 15)
        self.assertEqual(self.searcher.user_agent, 'Test Bot')
    
    @patch('requests.get')
    @patch('bs4.BeautifulSoup')
    def test_fallback_search_success(self, mock_soup, mock_get):
        """测试备用搜索成功"""
        # 模拟HTTP响应
        mock_response = Mock()
        mock_response.text = '<html><body></body></html>'
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        # 模拟BeautifulSoup解析
        mock_element = Mock()
        mock_element.find.return_value = Mock()
        mock_element.find.return_value.get_text.return_value = 'Test Title'
        mock_element.find.return_value.get.return_value = 'https://example.com'
        
        mock_soup_instance = Mock()
        mock_soup_instance.find_all.return_value = [mock_element]
        mock_soup.return_value = mock_soup_instance
        
        results = self.searcher.search("test query", max_results=1)
        
        # 验证请求被调用
        mock_get.assert_called_once()
        self.assertIsInstance(results, list)


class TestSearchHandler(unittest.TestCase):
    """搜索处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            'max_results': 5,
            'timeout': 30,
            'language': 'zh',
            'region': 'CN',
            'mcp': {
                'enabled': True,
                'server_url': 'ws://localhost:8080'
            },
            'fallback': {
                'enabled': True
            }
        }
        
    @patch('src.processors.ai_agent_components.components.search_handler.MCP_AVAILABLE', False)
    def test_search_handler_no_mcp(self):
        """测试MCP不可用时的搜索处理器"""
        handler = SearchHandler(self.config)
        self.assertEqual(handler.search_mode, "mock")
        self.assertIsNone(handler.mcp_client)
    
    def test_search_handler_initialization(self):
        """测试搜索处理器初始化"""
        handler = SearchHandler(self.config)
        self.assertEqual(handler.max_results, 5)
        self.assertEqual(handler.timeout, 30)
        self.assertEqual(handler.default_language, 'zh')
        self.assertEqual(handler.default_region, 'CN')
    
    def test_search_with_mock_mode(self):
        """测试模拟模式搜索"""
        handler = SearchHandler(self.config)
        handler.search_mode = "mock"  # 强制使用模拟模式
        
        context = handler.search("test query", SearchType.GENERAL)
        
        self.assertIsNotNone(context)
        self.assertEqual(context.request.query, "test query")
        self.assertGreater(len(context.results), 0)
        self.assertIsNone(context.error_message)
    
    def test_get_search_statistics(self):
        """测试获取搜索统计信息"""
        handler = SearchHandler(self.config)
        stats = handler.get_search_statistics()
        
        self.assertIn('search_mode', stats)
        self.assertIn('mcp_available', stats)
        self.assertIn('fallback_available', stats)
        self.assertIn('mcp_connection_status', stats)
        self.assertIsInstance(stats['mcp_available'], bool)
        self.assertIsInstance(stats['fallback_available'], bool)
    
    def test_text_similarity(self):
        """测试文本相似度计算"""
        handler = SearchHandler(self.config)
        
        # 完全匹配
        similarity = handler._text_similarity("hello world", "hello world")
        self.assertEqual(similarity, 1.0)
        
        # 部分匹配
        similarity = handler._text_similarity("hello world test", "hello world")
        self.assertEqual(similarity, 1.0)  # 查询词都在文本中
        
        # 无匹配
        similarity = handler._text_similarity("completely different", "hello world")
        self.assertEqual(similarity, 0.0)
        
        # 空文本
        similarity = handler._text_similarity("", "hello world")
        self.assertEqual(similarity, 0.0)
    
    def test_calculate_source_authority(self):
        """测试来源权威性计算"""
        handler = SearchHandler(self.config)
        
        # 权威来源
        authority = handler._calculate_source_authority("政府官方网站")
        self.assertEqual(authority, 0.8)
        
        # 普通来源
        authority = handler._calculate_source_authority("普通网站")
        self.assertEqual(authority, 0.5)
    
    def test_calculate_freshness(self):
        """测试时效性计算"""
        handler = SearchHandler(self.config)
        
        # 无日期
        freshness = handler._calculate_freshness(None)
        self.assertEqual(freshness, 0.5)
        
        # 最近日期
        from datetime import datetime, timedelta
        recent_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
        freshness = handler._calculate_freshness(recent_date)
        self.assertEqual(freshness, 1.0)


if __name__ == '__main__':
    unittest.main()
