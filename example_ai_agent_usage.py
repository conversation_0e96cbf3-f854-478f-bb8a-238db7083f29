#!/usr/bin/env python3
"""
AI Agent 系统使用示例
展示如何使用升级版的AI回复处理器
"""

import os
import sys
import json
from typing import Dict, Any

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from loguru import logger
from src.processors.ai_reply_processor import AIReplyProcessor
from src.core.ai_analyzer import AnalysisResult


def create_sample_email_data(email_type: str = "complex") -> Dict[str, Any]:
    """创建示例邮件数据"""
    
    if email_type == "simple":
        return {
            "subject": "问候",
            "sender": "<EMAIL>",
            "body": "你好！最近怎么样？",
            "attachments": [],
            "date": "2024-01-15 10:30:00",
            "message_id": "simple-001",
            "uid": "uid-001",
            "folder": "INBOX"
        }
    
    elif email_type == "technical":
        return {
            "subject": "系统故障求助",
            "sender": "<EMAIL>",
            "body": """
            您好！
            
            我们的系统出现了以下问题：
            1. 数据库连接超时
            2. API响应缓慢
            3. 用户登录失败
            
            请帮助分析原因并提供解决方案。
            
            谢谢！
            """,
            "attachments": [],
            "date": "2024-01-15 14:20:00",
            "message_id": "tech-001",
            "uid": "uid-002",
            "folder": "INBOX"
        }
    
    elif email_type == "research":
        return {
            "subject": "市场调研请求",
            "sender": "<EMAIL>",
            "body": """
            您好！
            
            我们需要进行一项关于电动汽车市场的调研，具体包括：
            
            1. 全球电动汽车销量趋势分析
            2. 主要厂商市场份额对比
            3. 技术发展路线图
            4. 政策影响分析
            5. 未来3年市场预测
            
            请提供详细的研究报告和数据支持。
            
            期待您的回复！
            """,
            "attachments": [],
            "date": "2024-01-15 16:45:00",
            "message_id": "research-001",
            "uid": "uid-003",
            "folder": "INBOX"
        }
    
    else:  # complex
        return {
            "subject": "关于人工智能发展趋势的咨询",
            "sender": "<EMAIL>",
            "body": """
            您好！
            
            我想了解一下当前人工智能技术的发展趋势，特别是在以下几个方面：
            1. 大语言模型的最新进展
            2. AI在企业应用中的实际案例
            3. 未来5年的发展预测
            
            希望能得到详细的分析和建议。
            
            谢谢！
            """,
            "attachments": [],
            "date": "2024-01-15 10:30:00",
            "message_id": "complex-001",
            "uid": "uid-004",
            "folder": "INBOX"
        }


def create_sample_analysis_result(email_type: str = "complex") -> AnalysisResult:
    """创建示例分析结果"""
    
    if email_type == "simple":
        return AnalysisResult(
            category="个人",
            priority="低",
            sentiment="积极",
            summary="简单问候邮件",
            action_required=True,
            suggested_actions=["友好回复"],
            processor_suggestions=[
                {
                    "processor_name": "ai_reply_processor",
                    "reason": "简单问候回复",
                    "priority": 1,
                    "enabled": True
                }
            ],
            confidence=0.95
        )
    
    elif email_type == "technical":
        return AnalysisResult(
            category="技术支持",
            priority="高",
            sentiment="中性",
            summary="系统故障求助",
            action_required=True,
            suggested_actions=["技术分析", "解决方案"],
            processor_suggestions=[
                {
                    "processor_name": "ai_reply_processor",
                    "reason": "需要技术支持回复",
                    "priority": 1,
                    "enabled": True
                }
            ],
            confidence=0.90
        )
    
    elif email_type == "research":
        return AnalysisResult(
            category="研究请求",
            priority="中",
            sentiment="中性",
            summary="电动汽车市场调研请求",
            action_required=True,
            suggested_actions=["深度研究", "数据分析", "报告生成"],
            processor_suggestions=[
                {
                    "processor_name": "ai_reply_processor",
                    "reason": "需要研究型回复",
                    "priority": 1,
                    "enabled": True
                }
            ],
            confidence=0.88
        )
    
    else:  # complex
        return AnalysisResult(
            category="技术咨询",
            priority="中",
            sentiment="中性",
            summary="用户咨询人工智能发展趋势",
            action_required=True,
            suggested_actions=["提供详细分析", "搜索最新信息"],
            processor_suggestions=[
                {
                    "processor_name": "ai_reply_processor",
                    "reason": "需要AI生成详细回复",
                    "priority": 1,
                    "enabled": True
                }
            ],
            confidence=0.85
        )


def demo_ai_agent_system():
    """演示AI Agent系统的使用"""
    
    logger.info("=== AI Agent 系统使用演示 ===")
    
    # 配置AI Agent系统
    config = {
        "enabled": True,
        "ai_prompt": "请根据以下邮件内容生成专业、友好的回复：",
        
        # 搜索配置
        "search": {
            "max_results": 5,
            "timeout": 30,
            "language": "zh",
            "region": "CN"
        },
        
        # 分析配置
        "analysis": {
            "max_content_length": 2000,
            "min_confidence_threshold": 0.6,
            "enable_deep_analysis": True
        },
        
        # 任务执行配置
        "task_execution": {
            "max_execution_time": 300,
            "enable_parallel_execution": False,
            "retry_failed_steps": True,
            "max_retries": 2
        },
        
        # 回复生成配置
        "response_generation": {
            "max_reply_length": 1000,
            "include_sources": True,
            "professional_tone": True,
            "language": "zh"
        }
    }
    
    # 创建AI回复处理器实例
    processor = AIReplyProcessor("ai_reply_processor", config)
    
    logger.info(f"AI Agent模式: {'增强模式' if processor.use_enhanced_mode else '传统模式'}")
    
    # 测试不同类型的邮件
    test_cases = [
        ("简单问候", "simple"),
        ("技术支持", "technical"),
        ("研究请求", "research"),
        ("复杂咨询", "complex")
    ]
    
    for case_name, email_type in test_cases:
        logger.info(f"\n--- 测试案例: {case_name} ---")
        
        # 创建测试数据
        email_data = create_sample_email_data(email_type)
        analysis = create_sample_analysis_result(email_type)
        
        logger.info(f"邮件主题: {email_data['subject']}")
        logger.info(f"邮件类型: {email_type}")
        
        # 检查是否可以处理
        can_process = processor.can_process(email_data, analysis)
        logger.info(f"可以处理: {can_process}")
        
        if can_process:
            try:
                # 执行处理
                result = processor.process(email_data, analysis)
                
                # 显示结果摘要
                logger.info("处理结果摘要:")
                logger.info(f"  - 回复已发送: {result.get('reply_sent', False)}")
                logger.info(f"  - 处理模式: {result.get('mode', 'unknown')}")
                logger.info(f"  - 收件人: {result.get('recipient', 'N/A')}")
                
                if result.get('mode') == 'enhanced':
                    logger.info(f"  - 任务ID: {result.get('task_id', 'N/A')}")
                    logger.info(f"  - 任务类型: {result.get('task_type', 'N/A')}")
                    logger.info(f"  - 意图类型: {result.get('intent_type', 'N/A')}")
                    logger.info(f"  - 置信度: {result.get('confidence', 0):.2f}")
                    logger.info(f"  - 执行时间: {result.get('execution_time', 0):.3f}秒")
                    logger.info(f"  - 执行步骤: {result.get('steps_executed', 0)}个")
                
                # 显示生成的回复（截取前200字符）
                ai_reply = result.get('ai_reply', '')
                if ai_reply:
                    preview = ai_reply[:200] + "..." if len(ai_reply) > 200 else ai_reply
                    logger.info(f"回复预览: {preview}")
                
            except Exception as e:
                logger.error(f"处理失败: {e}")
        
        logger.info("-" * 50)


def demo_intent_classification():
    """演示意图识别功能"""
    
    logger.info("\n=== 意图识别功能演示 ===")
    
    try:
        from src.processors.ai_agent_components.utils.intent_classifier import IntentClassifier
        
        classifier = IntentClassifier()
        
        # 测试不同类型的邮件意图识别
        test_emails = [
            ("什么是人工智能？", "简单问题"),
            ("请详细分析市场趋势", "复杂询问"),
            ("系统出现故障，无法登录", "技术支持"),
            ("需要最新的销售数据", "信息请求"),
            ("你好，最近怎么样？", "问候"),
        ]
        
        for email_body, expected_type in test_emails:
            email_data = {
                "subject": "测试邮件",
                "body": email_body,
                "sender": "<EMAIL>"
            }
            
            intent_result = classifier.classify_intent(email_data)
            
            logger.info(f"邮件内容: {email_body}")
            logger.info(f"预期类型: {expected_type}")
            logger.info(f"识别结果: {intent_result.intent_type.value}")
            logger.info(f"置信度: {intent_result.confidence:.2f}")
            logger.info(f"任务类型: {intent_result.task_type.value}")
            logger.info(f"需要搜索: {intent_result.requires_search}")
            logger.info("-" * 30)
            
    except ImportError:
        logger.error("意图分类器组件未找到")


if __name__ == "__main__":
    # 演示AI Agent系统
    demo_ai_agent_system()
    
    # 演示意图识别
    demo_intent_classification()
    
    logger.info("\n=== 演示完成 ===")
    logger.info("AI Agent系统已成功升级，支持智能邮件处理！")
