#!/usr/bin/env python3
"""
完整邮件处理流程测试
验证从邮件接收到AI回复的整个流程，包括智谱AI搜索集成
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.core.config_manager import ConfigManager
from src.core.ai_analyzer import AIAnalyzer
from src.processors.ai_reply_processor import AIReplyProcessor

def test_complete_email_flow():
    """测试完整的邮件处理流程"""
    logger.info("🚀 开始完整邮件处理流程测试")
    logger.info("=" * 80)
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        # 创建AI分析器和回复处理器
        ai_analyzer = AIAnalyzer(config.ai)
        ai_processor = AIReplyProcessor(config)
        
        # 测试邮件数据
        test_emails = [
            {
                "subject": "咨询人工智能在医疗领域的最新应用",
                "body": "您好，我是一名医生，想了解人工智能技术在医疗诊断和治疗方面的最新进展。特别是在影像诊断、药物研发和个性化治疗方面有哪些突破？请提供一些具体的案例和技术细节。谢谢！",
                "from": "<EMAIL>",
                "to": "<EMAIL>"
            },
            {
                "subject": "区块链技术在供应链管理中的应用",
                "body": "我们公司正在考虑在供应链管理中引入区块链技术，希望了解当前的技术成熟度、实施成本和预期效果。能否提供一些成功案例和实施建议？",
                "from": "<EMAIL>",
                "to": "<EMAIL>"
            },
            {
                "subject": "Python机器学习项目开发指导",
                "body": "我是一名初学者，想开始一个Python机器学习项目。请推荐一些适合新手的库和工具，以及学习路径。另外，有哪些实际项目可以练手？",
                "from": "<EMAIL>",
                "to": "<EMAIL>"
            }
        ]
        
        # 处理每封邮件
        for i, email_data in enumerate(test_emails, 1):
            logger.info(f"\n📧 处理第 {i} 封邮件")
            logger.info(f"主题: {email_data['subject']}")
            logger.info(f"发件人: {email_data['from']}")
            logger.info("-" * 60)
            
            try:
                # 首先进行AI分析
                analysis = ai_analyzer.analyze_email(email_data)
                logger.info(f"AI分析完成: 类别={analysis.category}, 置信度={analysis.confidence:.2f}")

                # 然后处理邮件
                result = ai_processor.process(email_data, analysis)
                
                if result.get('success', False):
                    logger.success("✅ 邮件处理成功!")
                    
                    # 显示处理结果
                    if 'analysis' in result:
                        analysis = result['analysis']
                        logger.info(f"📊 AI分析结果:")
                        logger.info(f"  查询意图: {analysis.get('query_intent', 'N/A')}")
                        logger.info(f"  建议操作: {analysis.get('suggested_actions', [])}")
                        logger.info(f"  置信度: {analysis.get('confidence', 0):.2f}")
                    
                    if 'search_results' in result:
                        search_results = result['search_results']
                        logger.info(f"🔍 搜索结果: 找到 {len(search_results)} 个相关结果")
                        for j, search_result in enumerate(search_results[:2], 1):
                            logger.info(f"  {j}. {search_result.get('title', 'N/A')}")
                    
                    if 'response' in result:
                        response = result['response']
                        logger.info(f"💬 AI回复预览:")
                        logger.info(f"  {response[:200]}...")
                    
                    logger.info(f"⏱️ 处理时间: {result.get('processing_time', 0):.2f}秒")
                    
                else:
                    logger.error(f"❌ 邮件处理失败: {result.get('error', '未知错误')}")
                
            except Exception as e:
                logger.error(f"❌ 处理邮件时发生异常: {e}")
            
            logger.info("=" * 60)
            
            # 等待一下避免请求过快
            import time
            time.sleep(3)
        
        logger.success("🎉 完整邮件处理流程测试完成!")
        
    except Exception as e:
        logger.error(f"❌ 完整流程测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def test_ai_agent_features():
    """测试AI Agent的高级功能"""
    logger.info("\n🤖 测试AI Agent高级功能")
    logger.info("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        # 创建AI分析器和回复处理器
        ai_analyzer = AIAnalyzer(config.ai)
        ai_processor = AIReplyProcessor(config)
        
        # 测试复杂查询邮件
        complex_email = {
            "subject": "多技术栈集成方案咨询",
            "body": """
            您好，我们公司正在规划一个大型项目，需要集成以下技术：
            1. 微服务架构（Spring Boot + Docker）
            2. 前端框架（React + TypeScript）
            3. 数据库（PostgreSQL + Redis）
            4. 消息队列（RabbitMQ）
            5. 监控系统（Prometheus + Grafana）
            
            请提供：
            - 技术栈兼容性分析
            - 架构设计建议
            - 性能优化方案
            - 部署策略
            - 潜在风险评估
            
            项目预计6个月完成，团队规模15人。
            """,
            "from": "<EMAIL>",
            "to": "<EMAIL>"
        }
        
        logger.info("📧 处理复杂技术咨询邮件")
        logger.info(f"主题: {complex_email['subject']}")
        logger.info("-" * 60)
        
        # 首先进行AI分析
        analysis = ai_analyzer.analyze_email(complex_email)
        logger.info(f"复杂邮件AI分析: 类别={analysis.category}, 置信度={analysis.confidence:.2f}")

        # 处理复杂邮件
        result = ai_processor.process(complex_email, analysis)
        
        if result.get('success', False):
            logger.success("✅ 复杂邮件处理成功!")
            
            # 详细分析结果
            if 'analysis' in result:
                analysis = result['analysis']
                logger.info("📊 详细分析结果:")
                logger.info(f"  查询类型: {analysis.get('query_type', 'N/A')}")
                logger.info(f"  复杂度: {analysis.get('complexity', 'N/A')}")
                logger.info(f"  关键词: {analysis.get('keywords', [])}")
                logger.info(f"  建议操作: {analysis.get('suggested_actions', [])}")
            
            # 搜索结果分析
            if 'search_results' in result:
                search_results = result['search_results']
                logger.info(f"🔍 搜索结果分析:")
                logger.info(f"  总结果数: {len(search_results)}")
                
                # 按来源分类
                sources = {}
                for search_result in search_results:
                    source = search_result.get('source', 'Unknown')
                    sources[source] = sources.get(source, 0) + 1
                
                logger.info(f"  来源分布: {sources}")
            
            # 回复质量评估
            if 'response' in result:
                response = result['response']
                logger.info(f"💬 回复质量评估:")
                logger.info(f"  回复长度: {len(response)} 字符")
                logger.info(f"  包含技术关键词: {'微服务' in response or 'Docker' in response}")
                logger.info(f"  结构化回复: {'1.' in response or '一、' in response}")
        
        else:
            logger.error(f"❌ 复杂邮件处理失败: {result.get('error', '未知错误')}")
        
        logger.success("🎉 AI Agent高级功能测试完成!")
        
    except Exception as e:
        logger.error(f"❌ AI Agent功能测试失败: {e}")

def test_performance_metrics():
    """测试性能指标"""
    logger.info("\n📈 测试性能指标")
    logger.info("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        # 创建AI分析器和回复处理器
        ai_analyzer = AIAnalyzer(config.ai)
        ai_processor = AIReplyProcessor(config)
        
        # 简单邮件用于性能测试
        simple_email = {
            "subject": "Python学习资源推荐",
            "body": "请推荐一些Python学习资源，包括书籍、在线课程和实践项目。",
            "from": "<EMAIL>",
            "to": "<EMAIL>"
        }
        
        import time
        
        # 执行多次测试
        times = []
        success_count = 0
        
        for i in range(3):
            logger.info(f"执行第 {i+1} 次性能测试...")
            
            start_time = time.time()
            # 首先进行AI分析
            analysis = ai_analyzer.analyze_email(simple_email)
            # 然后处理邮件
            result = ai_processor.process(simple_email, analysis)
            end_time = time.time()
            
            processing_time = end_time - start_time
            times.append(processing_time)
            
            if result.get('success', False):
                success_count += 1
                logger.info(f"  ✅ 成功，耗时: {processing_time:.2f}秒")
            else:
                logger.info(f"  ❌ 失败，耗时: {processing_time:.2f}秒")
            
            time.sleep(2)  # 避免请求过快
        
        # 计算性能指标
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        success_rate = success_count / len(times) * 100
        
        logger.info("📊 性能指标总结:")
        logger.info(f"  平均处理时间: {avg_time:.2f}秒")
        logger.info(f"  最快处理时间: {min_time:.2f}秒")
        logger.info(f"  最慢处理时间: {max_time:.2f}秒")
        logger.info(f"  成功率: {success_rate:.1f}%")
        
        # 性能评估
        if avg_time < 30:
            logger.success("✅ 性能优秀：平均处理时间小于30秒")
        elif avg_time < 60:
            logger.info("⚠️ 性能良好：平均处理时间小于60秒")
        else:
            logger.warning("⚠️ 性能需要优化：平均处理时间超过60秒")
        
        if success_rate >= 90:
            logger.success("✅ 可靠性优秀：成功率≥90%")
        elif success_rate >= 70:
            logger.info("⚠️ 可靠性良好：成功率≥70%")
        else:
            logger.warning("⚠️ 可靠性需要改进：成功率<70%")
        
        logger.success("🎉 性能指标测试完成!")
        
    except Exception as e:
        logger.error(f"❌ 性能测试失败: {e}")

def main():
    """主测试函数"""
    logger.info("🎯 开始完整邮件处理流程验证")
    logger.info("=" * 80)
    
    # 1. 完整邮件处理流程测试
    test_complete_email_flow()
    
    # 2. AI Agent高级功能测试
    test_ai_agent_features()
    
    # 3. 性能指标测试
    test_performance_metrics()
    
    logger.info("=" * 80)
    logger.success("🏆 所有测试完成！智谱AI MCP客户端集成验证成功！")

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    main()
