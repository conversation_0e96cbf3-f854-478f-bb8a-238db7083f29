#!/usr/bin/env python3
"""
MCP客户端使用示例
演示如何使用MCP客户端进行网络搜索
"""

import sys
import os
import asyncio
from typing import Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from processors.ai_agent_components.components.mcp_client import MCPClient
from processors.ai_agent_components.components.search_handler import SearchHandler
from processors.ai_agent_components.models.search_models import SearchType


def demo_search_handler():
    """演示搜索处理器的使用"""
    print("🔍 搜索处理器演示")
    print("=" * 50)
    
    # 配置搜索处理器
    config = {
        'max_results': 3,
        'timeout': 10,
        'language': 'zh',
        'region': 'CN',
        'mcp': {
            'enabled': True,
            'server_url': 'ws://localhost:8080',
            'timeout': 5,
            'max_retries': 1,  # 减少重试次数用于演示
            'zhipu_search': {
                'preferred_engines': ['google', 'bing'],
                'quality_settings': {
                    'relevance_threshold': 0.6
                }
            }
        },
        'fallback': {
            'enabled': True,
            'timeout': 10
        }
    }
    
    # 初始化搜索处理器
    handler = SearchHandler(config)
    
    # 显示搜索统计信息
    stats = handler.get_search_statistics()
    print(f"搜索模式: {stats['search_mode']}")
    print(f"MCP可用: {stats['mcp_available']}")
    print(f"备用搜索可用: {stats['fallback_available']}")
    print(f"MCP连接状态: {stats['mcp_connection_status']}")
    print()
    
    # 执行搜索
    queries = [
        ("人工智能发展趋势", SearchType.TECHNICAL),
        ("Python编程教程", SearchType.GENERAL),
        ("机器学习算法", SearchType.ACADEMIC)
    ]
    
    for query, search_type in queries:
        print(f"🔎 搜索: {query} (类型: {search_type.value})")
        
        try:
            context = handler.search(query, search_type, max_results=2)
            
            if context.error_message:
                print(f"   ❌ 搜索出错: {context.error_message}")
            else:
                print(f"   ✅ 找到 {len(context.results)} 个结果，耗时 {context.search_time:.2f}秒")
                
                for i, result in enumerate(context.results, 1):
                    print(f"   {i}. {result.title}")
                    print(f"      URL: {result.url}")
                    print(f"      摘要: {result.snippet[:100]}...")
                    print(f"      相关性: {result.relevance_score:.2f}")
                    print()
        
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")
        
        print("-" * 30)


async def demo_mcp_client():
    """演示MCP客户端的异步使用"""
    print("\n🌐 MCP客户端异步演示")
    print("=" * 50)
    
    config = {
        'server_url': 'ws://localhost:8080',
        'timeout': 5,
        'max_retries': 1,
        'zhipu_search': {
            'preferred_engines': ['google'],
            'cache': {
                'enabled': True,
                'ttl': 3600
            }
        }
    }
    
    # 使用异步上下文管理器
    try:
        async with MCPClient(config) as client:
            print(f"连接状态: {client.connection_state}")
            
            # 尝试搜索
            results = await client.search_web(
                query="Python异步编程",
                max_results=3,
                language="zh",
                search_type="technical"
            )
            
            print(f"搜索结果数量: {len(results)}")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.get('title', 'N/A')}")
                print(f"   URL: {result.get('url', 'N/A')}")
                print()
    
    except Exception as e:
        print(f"MCP客户端演示失败: {e}")
        print("这是正常的，因为没有运行真实的MCP服务器")


def demo_configuration():
    """演示配置选项"""
    print("\n⚙️  配置选项演示")
    print("=" * 50)
    
    # 基本配置
    basic_config = {
        'mcp': {
            'enabled': True,
            'server_url': 'ws://localhost:8080',
            'timeout': 30
        }
    }
    
    # 高级配置
    advanced_config = {
        'mcp': {
            'enabled': True,
            'server_url': 'ws://your-mcp-server:8080',
            'api_key': 'your-api-key',
            'timeout': 30,
            'max_retries': 3,
            'retry_delay': 1.0,
            'connection_pool_size': 5,
            'heartbeat_interval': 30,
            'zhipu_search': {
                'preferred_engines': ['google', 'bing', 'baidu'],
                'content_filter': {
                    'min_content_length': 50,
                    'exclude_domains': ['spam-site.com']
                },
                'quality_settings': {
                    'relevance_threshold': 0.6,
                    'freshness_weight': 0.2,
                    'authority_weight': 0.3
                },
                'cache': {
                    'enabled': True,
                    'ttl': 3600,
                    'max_entries': 1000
                }
            },
            'fallback': {
                'enabled': True,
                'search_engine': 'duckduckgo',
                'timeout': 15,
                'max_results': 5
            }
        }
    }
    
    print("基本配置示例:")
    import json
    print(json.dumps(basic_config, indent=2, ensure_ascii=False))
    
    print("\n高级配置示例:")
    print(json.dumps(advanced_config, indent=2, ensure_ascii=False))


def demo_error_handling():
    """演示错误处理和回退机制"""
    print("\n🛡️  错误处理演示")
    print("=" * 50)
    
    # 配置一个会失败的MCP连接
    config = {
        'mcp': {
            'enabled': True,
            'server_url': 'ws://nonexistent-server:8080',
            'timeout': 2,
            'max_retries': 1
        },
        'fallback': {
            'enabled': False  # 禁用备用搜索以演示错误处理
        }
    }
    
    handler = SearchHandler(config)
    
    print("尝试连接到不存在的MCP服务器...")
    context = handler.search("测试查询", max_results=1)
    
    if context.error_message:
        print(f"✅ 错误处理正常: {context.error_message}")
    else:
        print(f"✅ 使用了备用搜索，找到 {len(context.results)} 个结果")
    
    # 演示搜索模式切换
    print(f"\n当前搜索模式: {handler.search_mode}")
    
    # 启用备用搜索
    handler.config['fallback'] = {'enabled': True}
    handler._initialize_search_clients()
    
    print(f"重新初始化后的搜索模式: {handler.search_mode}")


def main():
    """主函数"""
    print("🚀 MCP客户端功能演示")
    print("=" * 60)
    
    try:
        # 1. 搜索处理器演示
        demo_search_handler()
        
        # 2. 异步MCP客户端演示
        asyncio.run(demo_mcp_client())
        
        # 3. 配置选项演示
        demo_configuration()
        
        # 4. 错误处理演示
        demo_error_handling()
        
        print("\n🎉 演示完成！")
        print("\n💡 提示:")
        print("   - 要使用真实的MCP搜索，请启动zhipu-web-search-sse MCP服务器")
        print("   - 配置正确的服务器URL和API密钥")
        print("   - 查看配置文件示例: config/config_mcp_example.yaml")
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
