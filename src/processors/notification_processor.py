"""
通知处理器 - 发送重要邮件通知
"""

import os
import sys
import subprocess
from typing import Dict, Any, Optional
from loguru import logger

# 添加项目根目录到sys.path
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(base_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.processor_manager import BaseProcessor
from src.core.ai_analyzer import AnalysisResult

try:
    from src.utils.string_utils import escape_apple_script, clean_text_for_notification  # 导入字符串处理函数
except ImportError:
    # 如果没有string_utils，提供简单的处理函数
    def clean_text_for_notification(text: str) -> str:
        """简单的文本清理"""
        if not text:
            return ""
        # 移除换行符和制表符
        cleaned = text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
        # 压缩空格
        import re
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        # 限制长度
        if len(cleaned) > 100:
            cleaned = cleaned[:97] + "..."
        return cleaned

    def escape_apple_script(text: str) -> str:
        """简单的AppleScript字符串转义"""
        if not text:
            return ""
        cleaned = clean_text_for_notification(text)
        return cleaned.replace('"', '\\"').replace("\\", "\\\\")


class NotificationProcessor(BaseProcessor):
    """通知处理器 - 对重要邮件发送系统通知"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.priority_threshold = config.get('priority_threshold', '高') if config else '高'
        self.notification_method = config.get('method', 'system') if config else 'system'
        
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """只处理高优先级邮件"""
        return analysis.priority == self.priority_threshold
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """发送通知"""
        # 获取并清理邮件信息
        raw_subject = email_data.get('subject', '无主题')
        raw_sender = email_data.get('sender', '未知发件人')

        # 清理文本用于通知显示
        clean_subject = clean_text_for_notification(raw_subject)
        clean_sender = clean_text_for_notification(raw_sender)
        clean_summary = clean_text_for_notification(analysis.summary)

        # 构建通知内容
        notification_title = f"重要邮件: {clean_subject[:50]}"
        notification_body = f"发件人: {clean_sender}\n摘要: {clean_summary}"

        success = False
        method_used = ""
        error_message = ""

        try:
            if self.notification_method == 'system':
                success = self._send_system_notification(notification_title, notification_body)
                method_used = "系统通知"
            elif self.notification_method == 'console':
                success = self._send_console_notification(notification_title, notification_body)
                method_used = "控制台通知"
            else:
                error_message = f"不支持的通知方式: {self.notification_method}"
                logger.warning(error_message)

        except Exception as e:
            error_message = f"发送通知失败: {e}"
            logger.error(error_message)
            # 如果系统通知失败，尝试控制台通知作为备用
            if self.notification_method == 'system':
                try:
                    logger.info("系统通知失败，尝试控制台通知作为备用")
                    success = self._send_console_notification(notification_title, notification_body)
                    method_used = "控制台通知(备用)"
                    error_message = ""
                except Exception as fallback_e:
                    error_message += f"; 备用通知也失败: {fallback_e}"
                    logger.error(f"备用通知也失败: {fallback_e}")

        result = {
            "notification_sent": success,
            "method": method_used,
            "title": notification_title,
            "body": notification_body,
            "original_subject": raw_subject,
            "original_sender": raw_sender
        }

        if error_message:
            result["error"] = error_message

        return result
    
    def _send_system_notification(self, title: str, body: str) -> bool:
        """发送系统通知"""
        try:
            # macOS
            if os.system("which osascript > /dev/null 2>&1") == 0:
                return self._send_macos_notification(title, body)

            # Linux (需要安装 notify-send)
            elif os.system("which notify-send > /dev/null 2>&1") == 0:
                return self._send_linux_notification(title, body)

            # Windows (需要安装 plyer 或使用 Windows Toast)
            else:
                logger.warning("当前系统不支持系统通知，使用控制台通知")
                return self._send_console_notification(title, body)

        except Exception as e:
            logger.error(f"系统通知发送失败: {e}")
            return False

    def _send_macos_notification(self, title: str, body: str) -> bool:
        """发送macOS系统通知"""
        try:
            # 转义特殊字符防止AppleScript语法错误
            escaped_title = escape_apple_script(title)
            escaped_body = escape_apple_script(body)

            # 记录调试信息
            logger.debug(f"发送macOS通知 - 原始标题: {title}")
            logger.debug(f"发送macOS通知 - 转义后标题: {escaped_title}")
            logger.debug(f"发送macOS通知 - 原始内容: {body}")
            logger.debug(f"发送macOS通知 - 转义后内容: {escaped_body}")

            # 构建AppleScript命令
            script = f'display notification "{escaped_body}" with title "{escaped_title}"'

            # 执行AppleScript命令
            result = subprocess.run(
                ['osascript', '-e', script],
                check=True,
                capture_output=True,
                text=True,
                timeout=10  # 设置超时防止卡死
            )

            logger.info("macOS系统通知发送成功")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"AppleScript执行失败: 返回码={e.returncode}")
            if e.stderr:
                logger.error(f"AppleScript错误输出: {e.stderr}")
            if e.stdout:
                logger.debug(f"AppleScript标准输出: {e.stdout}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("AppleScript执行超时")
            return False
        except Exception as e:
            logger.error(f"macOS通知发送失败: {e}")
            return False

    def _send_linux_notification(self, title: str, body: str) -> bool:
        """发送Linux系统通知"""
        try:
            # 清理文本用于Linux通知
            clean_title = clean_text_for_notification(title)
            clean_body = clean_text_for_notification(body)

            result = subprocess.run(
                ['notify-send', clean_title, clean_body],
                check=True,
                capture_output=True,
                text=True,
                timeout=10
            )

            logger.info("Linux系统通知发送成功")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"notify-send执行失败: 返回码={e.returncode}")
            if e.stderr:
                logger.error(f"notify-send错误输出: {e.stderr}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("notify-send执行超时")
            return False
        except Exception as e:
            logger.error(f"Linux通知发送失败: {e}")
            return False
    
    def _send_console_notification(self, title: str, body: str) -> bool:
        """发送控制台通知"""
        try:
            print("\n" + "="*60)
            print(f"🔔 {title}")
            print("-"*60)
            print(body)
            print("="*60 + "\n")
            return True
        except Exception as e:
            logger.error(f"控制台通知发送失败: {e}")
            return False
    
    def get_priority(self) -> int:
        """通知处理器优先级较高"""
        return 10
    
    def validate_config(self) -> bool:
        """验证配置"""
        valid_methods = ['system', 'console']
        if self.notification_method not in valid_methods:
            logger.error(f"无效的通知方式: {self.notification_method}")
            return False
        return True
