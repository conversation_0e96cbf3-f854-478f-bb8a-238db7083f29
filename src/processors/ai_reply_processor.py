"""
AI回复处理器 - 升级版AI Agent系统
支持网络搜索、复杂任务执行和智能回复生成
"""

import os
import sys
from typing import Dict, Any, Optional, TYPE_CHECKING
from loguru import logger

# 添加项目根目录到sys.path
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(base_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.processor_manager import BaseProcessor
from src.core.ai_analyzer import AnalysisResult
from src.utils.smtp_sender import send_email

# 类型导入（仅用于类型检查）
if TYPE_CHECKING:
    from .ai_agent_components.components.search_handler import SearchHandler
    from .ai_agent_components.components.analysis_engine import AnalysisEngine
    from .ai_agent_components.components.task_executor import TaskExecutor
    from .ai_agent_components.components.response_generator import ResponseGenerator
    from .ai_agent_components.utils.intent_classifier import IntentClassifier

# 运行时导入新的组件模块
SearchHandler = None
AnalysisEngine = None
TaskExecutor = None
ResponseGenerator = None
IntentClassifier = None

try:
    from .ai_agent_components.components.search_handler import SearchHandler
    from .ai_agent_components.components.analysis_engine import AnalysisEngine
    from .ai_agent_components.components.task_executor import TaskExecutor
    from .ai_agent_components.components.response_generator import ResponseGenerator
    from .ai_agent_components.utils.intent_classifier import IntentClassifier
    logger.info("AI Agent组件加载成功")
except ImportError as e:
    # 向后兼容：如果新组件不存在，使用传统模式
    logger.warning(f"新组件模块未找到，使用传统AI回复模式: {e}")
    SearchHandler = None
    AnalysisEngine = None
    TaskExecutor = None
    ResponseGenerator = None
    IntentClassifier = None

class AIReplyProcessor(BaseProcessor):
    """AI回复处理器 - 升级版AI Agent系统"""

    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.enabled = config.get('enabled', False) if config else False
        self.ai_prompt = config.get('ai_prompt', "请根据以下邮件内容生成合适的回复：") if config else "请根据以下邮件内容生成合适的回复："

        # 初始化组件属性
        self.use_enhanced_mode = False
        self.search_handler = None
        self.analysis_engine = None
        self.task_executor = None
        self.response_generator = None
        self.intent_classifier = None

        # 初始化新组件（如果可用）
        # 检查所有组件是否成功导入
        components_available = all([
            SearchHandler is not None,
            AnalysisEngine is not None,
            TaskExecutor is not None,
            ResponseGenerator is not None,
            IntentClassifier is not None
        ])

        # 尝试初始化增强模式组件
        if components_available:
            try:
                logger.info("尝试启用增强AI Agent模式")
                config = config or {}

                # 使用类型断言安全地初始化各个组件
                # 这里我们已经检查了组件的可用性，所以可以安全地进行类型断言
                assert SearchHandler is not None, "SearchHandler not available"
                assert AnalysisEngine is not None, "AnalysisEngine not available"
                assert TaskExecutor is not None, "TaskExecutor not available"
                assert ResponseGenerator is not None, "ResponseGenerator not available"
                assert IntentClassifier is not None, "IntentClassifier not available"

                self.search_handler = SearchHandler(config.get('search', {}))
                self.analysis_engine = AnalysisEngine(config.get('analysis', {}))
                self.task_executor = TaskExecutor(config.get('task_execution', {}))
                self.response_generator = ResponseGenerator(config.get('response_generation', {}))
                self.intent_classifier = IntentClassifier()

                self.use_enhanced_mode = True
                logger.info("增强AI Agent模式启用成功")

            except Exception as e:
                logger.warning(f"增强模式初始化失败，回退到传统模式: {e}")
                self.use_enhanced_mode = False
                self._init_traditional_mode()
        else:
            logger.info("组件未完全加载，使用传统AI回复模式")
            self.use_enhanced_mode = False
            self._init_traditional_mode()

    def _init_traditional_mode(self):
        """初始化传统模式"""
        self.search_handler = None
        self.analysis_engine = None
        self.task_executor = None
        self.response_generator = None
        self.intent_classifier = None
        
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """判断是否需要AI回复"""
        if not self.enabled:
            return False
            
        # 检查是否需要行动
        if not analysis.action_required:
            return False
            
        # 避免回复自动生成的邮件
        subject = email_data.get('subject', '').lower()
        if any(keyword in subject for keyword in ['re:', 'fwd:', 'auto-reply', 'out of office']):
            return False
            
        return True
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """使用AI生成回复并发送 - 支持增强模式和传统模式"""
        try:
            if self.use_enhanced_mode:
                return self._process_enhanced_mode(email_data, analysis)
            else:
                return self._process_traditional_mode(email_data, analysis)

        except Exception as e:
            logger.error(f"AI回复处理失败: {e}")
            return {
                "reply_sent": False,
                "error": str(e),
                "mode": "enhanced" if self.use_enhanced_mode else "traditional"
            }

    def _process_enhanced_mode(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """增强模式处理流程"""
        logger.info("使用增强AI Agent模式处理邮件")

        # 确保增强模式组件可用
        if not self.use_enhanced_mode:
            logger.error("增强模式组件不可用，回退到传统模式")
            return self._process_traditional_mode(email_data, analysis)

        # 类型检查：确保所有组件都已正确初始化
        if (self.intent_classifier is None or
            self.task_executor is None or
            self.response_generator is None):
            logger.error("增强模式组件未正确初始化，回退到传统模式")
            return self._process_traditional_mode(email_data, analysis)

        try:
            # 步骤1: 意图识别
            # 类型检查已在上面进行，这里可以安全使用
            intent_result = self.intent_classifier.classify_intent(email_data)  # type: ignore
            logger.info(f"意图识别: {intent_result.intent_type.value} (置信度: {intent_result.confidence:.2f})")

            # 步骤2: 创建任务上下文
            from .ai_agent_components.models.task_models import TaskContext
            import uuid

            task_context = TaskContext(
                task_id=str(uuid.uuid4()),
                task_type=intent_result.task_type,
                email_data=email_data,
                analysis_result=analysis
            )

            # 步骤3: 执行任务
            task_result = self.task_executor.execute_task(task_context)  # type: ignore

            # 步骤4: 生成最终回复
            search_context = None
            # 尝试从任务结果中获取搜索上下文
            for step in task_result.steps_executed:
                if step.result and step.result.get("search_context"):
                    search_context = step.result["search_context"]
                    break

            final_reply = self.response_generator.generate_response(  # type: ignore
                email_data, task_result, search_context
            )

            # 步骤5: 发送回复邮件
            self._send_reply_email(
                recipient=email_data.get('sender', ''),
                subject=f"Re: {email_data.get('subject', '')}",
                content=final_reply
            )

            return {
                "reply_sent": True,
                "recipient": email_data.get('sender', ''),
                "ai_reply": final_reply,
                "mode": "enhanced",
                "task_id": task_result.task_id,
                "task_type": task_result.task_type.value,
                "intent_type": intent_result.intent_type.value,
                "confidence": task_result.confidence_score,
                "execution_time": task_result.total_duration,
                "steps_executed": len(task_result.steps_executed)
            }

        except Exception as e:
            logger.error(f"增强模式处理失败，回退到传统模式: {e}")
            return self._process_traditional_mode(email_data, analysis)

    def _process_traditional_mode(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """传统模式处理流程"""
        logger.info("使用传统AI回复模式处理邮件")

        # 注意：analysis参数在传统模式中暂未使用，但保留用于接口一致性

        try:
            # 构建AI提示
            prompt = self._build_ai_prompt(email_data)

            # 调用AI服务生成回复
            ai_reply = self._call_ai_service(prompt)

            # 发送回复邮件
            self._send_reply_email(
                recipient=email_data.get('sender', ''),
                subject=f"Re: {email_data.get('subject', '')}",
                content=ai_reply
            )

            return {
                "reply_sent": True,
                "recipient": email_data.get('sender', ''),
                "ai_reply": ai_reply,
                "mode": "traditional"
            }

        except Exception as e:
            logger.error(f"传统模式处理失败: {e}")
            return {
                "reply_sent": False,
                "error": str(e),
                "mode": "traditional"
            }
    
    def _build_ai_prompt(self, email_data: Dict[str, Any]) -> str:
        """构建AI提示词"""
        return f"""
        {self.ai_prompt}
        
        邮件主题: {email_data.get('subject', '')}
        发件人: {email_data.get('sender', '')}
        邮件内容:
        {email_data.get('body', '')}
        """
    
    def _call_ai_service(self, prompt: str) -> str:
        """调用AI服务生成回复"""
        try:
            # 导入AI分析器配置
            from src.core.config_manager import ConfigManager
            from src.core.ai_analyzer import AIAnalyzer

            # 获取配置
            config_manager = ConfigManager()
            config = config_manager.load_config()

            # 创建AI分析器实例
            ai_analyzer = AIAnalyzer(config.ai)

            # 构建回复生成提示
            reply_prompt = f"""
            请根据以下邮件内容生成一个专业、友好的回复邮件：

            {prompt}

            回复要求：
            1. 语气要专业且友好
            2. 确认收到邮件
            3. 根据邮件内容提供有用的回复
            4. 如果需要进一步沟通，请提及
            5. 回复长度适中，不要过长

            请直接返回回复内容，不需要包含主题行或签名。
            """

            # 根据AI提供商类型调用相应的API
            provider = ai_analyzer.provider
            client = getattr(provider, 'client', None)
            if client:
                if ai_analyzer.config.provider.lower() == "openai":
                    # OpenAI API调用
                    response = client.chat.completions.create(
                        model=ai_analyzer.config.model,
                        messages=[
                            {"role": "system", "content": "你是一个专业的邮件助手，负责生成合适的邮件回复。"},
                            {"role": "user", "content": reply_prompt}
                        ],
                        max_tokens=500,
                        temperature=0.7
                    )
                    reply_content = response.choices[0].message.content
                elif ai_analyzer.config.provider.lower() == "anthropic":
                    # Anthropic API调用
                    response = client.messages.create(
                        model=ai_analyzer.config.model,
                        max_tokens=500,
                        temperature=0.7,
                        messages=[
                            {"role": "user", "content": reply_prompt}
                        ]
                    )
                    # 处理Anthropic响应格式
                    if hasattr(response, 'content') and isinstance(response.content, list) and len(response.content) > 0:
                        reply_content = getattr(response.content[0], 'text', None)
                    else:
                        reply_content = str(response.content)
                else:
                    reply_content = None

                if reply_content:
                    return reply_content.strip()
                else:
                    return "感谢您的邮件！我已收到您的消息，会尽快处理并回复您。"
            else:
                return "感谢您的邮件！我已收到您的消息，会尽快处理并回复您。"

        except Exception as e:
            logger.error(f"AI回复生成失败: {e}")
            # 如果AI服务失败，返回默认回复
            return "感谢您的邮件！我已收到您的消息，会尽快处理并回复您。"
    
    def _send_reply_email(self, recipient: str, subject: str, content: str):
        """发送回复邮件"""
        logger.info(f"准备发送AI回复给: {recipient}")
        logger.info(f"主题: {subject}")
        logger.info(f"内容: {content}")

        # 使用SMTP发送邮件
        try:
            success = send_email(
                to=recipient,
                subject=subject,
                body=content
            )

            if success:
                logger.info(f"AI回复邮件发送成功: {recipient}")
            else:
                logger.error(f"AI回复邮件发送失败: {recipient}")

        except Exception as e:
            logger.error(f"发送AI回复邮件时出错: {e}")
            # 如果SMTP发送失败，记录详细信息但不中断处理
            logger.info(f"邮件发送失败，但已记录处理日志")
    
    def get_priority(self) -> int:
        """AI回复处理器优先级较高"""
        return 30
    
    def validate_config(self) -> bool:
        """验证配置"""
        return True