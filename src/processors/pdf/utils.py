"""
PDF处理工具函数

提供PDF处理过程中使用的各种辅助工具函数。
"""

import hashlib
import re
from datetime import datetime
from pathlib import Path
from loguru import logger

# 添加项目根目录到sys.path
import sys
import os
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(base_dir)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.utils.smtp_sender import send_email


def calculate_file_hash(file_content: bytes) -> str:
    """
    计算文件哈希值
    
    Args:
        file_content: 文件内容
        
    Returns:
        str: SHA256哈希值
    """
    return hashlib.sha256(file_content).hexdigest()


def generate_safe_filename(filename: str) -> str:
    """
    生成安全的文件名
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 安全的文件名
    """
    # 移除不安全字符
    safe_name = re.sub(r'[^\w\-_\.]', '_', filename)
    
    # 确保有.pdf扩展名
    if not safe_name.lower().endswith('.pdf'):
        safe_name += '.pdf'
    
    # 添加时间戳避免冲突
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    name_parts = safe_name.rsplit('.', 1)
    if len(name_parts) == 2:
        safe_name = f"{name_parts[0]}_{timestamp}.{name_parts[1]}"
    else:
        safe_name = f"{safe_name}_{timestamp}"
    
    return safe_name


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        str: 格式化的文件大小字符串
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"


def get_current_time() -> str:
    """
    获取当前时间字符串

    Returns:
        str: 格式化的当前时间
    """
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def safe_format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    安全的日期时间格式化

    Args:
        dt: 日期时间对象
        format_str: 格式化字符串

    Returns:
        str: 格式化的日期时间字符串，如果失败则返回字符串表示
    """
    if not dt:
        return "未知"

    try:
        return dt.strftime(format_str)
    except (ValueError, TypeError) as e:
        logger.warning(f"日期格式化失败: {e}, 使用字符串表示")
        return str(dt)


def send_email_reply(to: str, subject: str, body: str) -> bool:
    """
    发送邮件回复
    
    Args:
        to: 收件人邮箱
        subject: 邮件主题
        body: 邮件正文
        
    Returns:
        bool: 发送是否成功
    """
    logger.info(f"准备发送邮件回复:")
    logger.info(f"收件人: {to}")
    logger.info(f"主题: {subject}")
    logger.info(f"内容: {body}")

    # 使用SMTP发送邮件
    try:
        success = send_email(
            to=to,
            subject=subject,
            body=body
        )

        if success:
            logger.info(f"PDF处理结果邮件发送成功: {to}")
            return True
        else:
            logger.error(f"PDF处理结果邮件发送失败: {to}")
            return False

    except Exception as e:
        logger.error(f"发送PDF处理结果邮件时出错: {e}")
        return False
