"""
PDF主处理器

协调各个组件完成PDF文件的完整处理流程。
"""

import os
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, List
from loguru import logger

# 添加项目根目录到sys.path
import sys
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(base_dir)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.ai_analyzer import AnalysisResult
from src.core.processor_manager import BaseProcessor

from .models import PDFProcessingResult, PDFProcessingStatus
from .exceptions import PDFProcessingError
from .validators import validate_pdf_file, is_pdf_attachment, MAX_PDF_SIZE
from .utils import generate_safe_filename, format_file_size, get_current_time, send_email_reply, safe_format_datetime
from .metadata_extractor import PDFMetadataExtractor
from .text_extractor import PDFTextExtractor
from .text_analyzer import PDFTextAnalyzer
from .warning_manager import PDFWarningManager, WarningCollector


class PdfProcessor(BaseProcessor):
    """
    高级PDF处理器
    
    功能特性：
    - 智能PDF文件检测和验证
    - 多引擎文本提取（docling + PyPDF2）
    - 详细的元数据分析
    - 高质量文本分析和关键词提取
    - 安全的文件处理和内存管理
    - 自动邮件回复和结果通知
    """
    
    def __init__(self, name: str = "pdf_processor", config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        
        # 基本配置
        self.enabled = self.config.get('enabled', True)
        self.max_file_size = self.config.get('max_file_size', MAX_PDF_SIZE)
        self.auto_reply = self.config.get('auto_reply', True)
        
        # 目录设置
        self.temp_dir = tempfile.gettempdir()
        self.download_path = Path(self.temp_dir) / "pdf_attachments"
        self.download_path.mkdir(exist_ok=True)
        
        # 初始化处理组件
        self.metadata_extractor = PDFMetadataExtractor()
        self.text_extractor = PDFTextExtractor()
        self.text_analyzer = PDFTextAnalyzer()
        
        # 性能监控
        self.processing_stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'total_processing_time': 0.0
        }
        
        logger.info(f"PDF处理器初始化完成 - 最大文件大小: {self.max_file_size / 1024 / 1024:.1f}MB")

    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """
        检查邮件是否包含可处理的PDF附件
        
        Args:
            email_data: 邮件数据
            analysis: AI分析结果
            
        Returns:
            bool: 是否可以处理
        """
        if not self.enabled:
            logger.debug("PDF处理器已禁用")
            return False

        # 检查是否有附件
        attachments = email_data.get('attachments', [])
        if not attachments:
            logger.debug("邮件无附件")
            return False

        logger.debug(f"检查邮件附件: {len(attachments)}个附件")

        # 检查每个附件
        for attachment in attachments:
            if is_pdf_attachment(attachment):
                filename = attachment.get('filename', 'unknown.pdf')
                logger.info(f"发现可处理的PDF附件: {filename}")
                return True

        logger.debug("未发现PDF附件")
        return False
    
    def _save_pdf_file(self, file_content: bytes, filename: str) -> str:
        """
        保存PDF文件到临时目录
        
        Args:
            file_content: PDF文件内容
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
            
        Raises:
            PDFProcessingError: 保存失败时抛出
        """
        try:
            # 验证文件
            validate_pdf_file(file_content, filename)
            
            # 生成安全的文件名
            safe_filename = generate_safe_filename(filename)
            filepath = self.download_path / safe_filename
            
            # 保存文件
            with open(filepath, 'wb') as f:
                f.write(file_content)
            
            logger.info(f"PDF文件保存成功: {safe_filename}, 大小: {len(file_content)} bytes")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"保存PDF文件失败: {e}")
            raise PDFProcessingError(f"保存PDF文件失败: {e}")
    
    def _process_single_pdf(self, attachment: Dict[str, Any]) -> PDFProcessingResult:
        """
        处理单个PDF文件

        Args:
            attachment: 附件信息

        Returns:
            PDFProcessingResult: 处理结果
        """
        start_time = datetime.now()
        filename = attachment.get('filename', 'unknown.pdf')

        result = PDFProcessingResult(
            filename=filename,
            status=PDFProcessingStatus.PENDING
        )

        # 使用警告收集器来监控处理过程中的警告
        with WarningCollector() as warning_collector:
            try:
                result.status = PDFProcessingStatus.PROCESSING

                # 获取文件内容
                file_content = attachment.get('content', b'')
                if not file_content:
                    raise PDFProcessingError("附件内容为空")

                # 保存文件
                filepath = self._save_pdf_file(file_content, filename)

                # 使用警告管理器抑制numpy警告
                with PDFWarningManager() as warning_manager:
                    # 提取元数据
                    logger.debug(f"开始提取元数据: {filename}")
                    result.metadata = self.metadata_extractor.extract_metadata(filepath, file_content)

                    # 提取文本
                    logger.debug(f"开始提取文本: {filename}")
                    text, extraction_method, confidence = self.text_extractor.extract_text(filepath)

                    # 分析文本
                    logger.debug(f"开始分析文本: {filename}")
                    result.text_analysis = self.text_analyzer.analyze_text(text, extraction_method, confidence)

                # 清理临时文件
                try:
                    os.unlink(filepath)
                    logger.debug(f"临时文件已清理: {filepath}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")

                result.status = PDFProcessingStatus.SUCCESS
                logger.info(f"PDF处理成功: {filename}")

            except Exception as e:
                result.status = PDFProcessingStatus.FAILED
                result.error_message = str(e)
                logger.error(f"PDF处理失败: {filename}, 错误: {e}")

            finally:
                # 计算处理时间
                end_time = datetime.now()
                result.processing_time = (end_time - start_time).total_seconds()

                # 记录警告摘要（如果有的话）
                warning_summary = warning_collector.get_warnings_summary()
                if warning_summary['total'] > 0:
                    logger.debug(f"PDF处理过程中收集到 {warning_summary['total']} 个警告: {warning_summary['by_category']}")

        return result

    def _generate_enhanced_reply(self, result: PDFProcessingResult) -> str:
        """
        生成增强的PDF处理结果邮件内容

        Args:
            result: PDF处理结果

        Returns:
            str: 邮件内容
        """
        if result.status != PDFProcessingStatus.SUCCESS:
            return self._generate_error_reply(result)

        metadata = result.metadata
        analysis = result.text_analysis

        reply_body = f"""您好！

您发送的PDF文件 '{result.filename}' 已成功处理完成。以下是详细的分析结果：

📄 **文档基本信息**
• 文件名: {result.filename}
• 文件大小: {format_file_size(metadata.file_size) if metadata else '未知'}
• 页数: {metadata.page_count if metadata else '未知'} 页
• 语言: {analysis.language if analysis else '未知'}
• 处理时间: {result.processing_time:.2f} 秒

📊 **文本统计**
• 总字数: {analysis.word_count if analysis else 0} 字
• 总字符数: {analysis.char_count if analysis else 0} 字符
• 有效字符数: {analysis.char_count_no_spaces if analysis else 0} 字符（不含空格）
• 段落数: {analysis.paragraph_count if analysis else 0} 段
• 行数: {analysis.line_count if analysis else 0} 行

🔍 **内容分析**
• 关键词: {', '.join(analysis.keywords) if analysis and analysis.keywords else '无'}
• 包含表格: {'是' if analysis and analysis.has_tables else '否'}
• 包含图片: {'是' if analysis and analysis.has_images else '否'}
• 提取方法: {analysis.extraction_method.value if analysis else '未知'}
• 提取置信度: {analysis.extraction_confidence:.2f if analysis else 0.0}

📝 **内容预览**
{analysis.text_preview if analysis else '无内容预览'}

📋 **文档元数据**"""

        if metadata:
            if metadata.title:
                reply_body += f"\n• 标题: {metadata.title}"
            if metadata.author:
                reply_body += f"\n• 作者: {metadata.author}"
            if metadata.subject:
                reply_body += f"\n• 主题: {metadata.subject}"
            if metadata.creator:
                reply_body += f"\n• 创建者: {metadata.creator}"
            if metadata.creation_date:
                formatted_date = safe_format_datetime(metadata.creation_date)
                reply_body += f"\n• 创建时间: {formatted_date}"
            if metadata.is_encrypted:
                reply_body += f"\n• 加密状态: 已加密"

        reply_body += f"""

---
此邮件由邮件处理机器人自动发送，如有问题请联系管理员。
处理时间: {get_current_time()}
文件哈希: {metadata.file_hash[:16] + '...' if metadata and metadata.file_hash else '未知'}
"""

        return reply_body

    def _generate_error_reply(self, result: PDFProcessingResult) -> str:
        """生成错误回复邮件"""
        return f"""您好！

很抱歉，您发送的PDF文件 '{result.filename}' 处理失败。

错误信息: {result.error_message or '未知错误'}
处理状态: {result.status.value}
处理时间: {result.processing_time:.2f} 秒

可能的原因：
• PDF文件损坏或格式不支持
• 文件过大（超过{self.max_file_size / 1024 / 1024:.1f}MB限制）
• PDF文件已加密
• 系统临时故障

建议：
• 请检查PDF文件是否完整
• 尝试重新发送文件
• 如问题持续，请联系管理员

---
此邮件由邮件处理机器人自动发送。
处理时间: {get_current_time()}
"""

    def _update_stats(self, result: PDFProcessingResult) -> None:
        """更新处理统计信息"""
        self.processing_stats['total_processed'] += 1
        self.processing_stats['total_processing_time'] += result.processing_time

        if result.status == PDFProcessingStatus.SUCCESS:
            self.processing_stats['successful'] += 1
        else:
            self.processing_stats['failed'] += 1

    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """
        处理PDF附件邮件

        Args:
            email_data: 邮件数据
            analysis: AI分析结果

        Returns:
            Dict[str, Any]: 处理结果
        """
        logger.info("开始处理PDF附件邮件")

        # 获取发送者信息
        sender = email_data.get('sender', '') or email_data.get('from', '')
        subject = email_data.get('subject', '无主题')

        if not sender:
            logger.error("无法获取邮件发送者信息，跳过PDF处理")
            return {
                "status": "error",
                "error": "无发送者信息",
                "pdf_processing_results": []
            }

        logger.debug(f"PDF处理器 - 发送者: {sender}, 主题: {subject}")

        # 处理所有PDF附件
        processing_results = []
        attachments = email_data.get('attachments', [])

        pdf_attachments = [att for att in attachments if is_pdf_attachment(att)]

        if not pdf_attachments:
            logger.info("未发现PDF附件，跳过处理")
            return {
                "status": "skipped",
                "message": "未发现PDF附件",
                "pdf_processing_results": []
            }

        logger.info(f"发现 {len(pdf_attachments)} 个PDF附件，开始处理")

        for attachment in pdf_attachments:
            try:
                # 处理单个PDF
                result = self._process_single_pdf(attachment)
                processing_results.append(result)

                # 更新统计信息
                self._update_stats(result)

                # 发送回复邮件（如果启用）
                if self.auto_reply:
                    try:
                        reply_subject = f"Re: {subject} - PDF处理完成"
                        reply_body = self._generate_enhanced_reply(result)

                        success = send_email_reply(
                            to=sender,
                            subject=reply_subject,
                            body=reply_body
                        )

                        result.reply_sent = success
                        if success:
                            logger.info(f"已发送PDF处理结果给 {sender}")
                        else:
                            logger.warning(f"发送PDF处理结果失败: {sender}")

                    except Exception as e:
                        logger.error(f"发送回复邮件失败: {e}")
                        result.reply_sent = False

            except Exception as e:
                logger.error(f"处理PDF附件时发生未预期错误: {e}")
                # 创建错误结果
                error_result = PDFProcessingResult(
                    filename=attachment.get('filename', 'unknown.pdf'),
                    status=PDFProcessingStatus.FAILED,
                    error_message=f"未预期错误: {str(e)}"
                )
                processing_results.append(error_result)
                self._update_stats(error_result)

        # 生成处理摘要
        successful_count = sum(1 for r in processing_results if r.status == PDFProcessingStatus.SUCCESS)
        failed_count = len(processing_results) - successful_count

        logger.info(f"PDF处理完成 - 成功: {successful_count}, 失败: {failed_count}")

        return {
            "status": "completed",
            "summary": {
                "total_processed": len(processing_results),
                "successful": successful_count,
                "failed": failed_count,
                "processing_time": sum(r.processing_time for r in processing_results)
            },
            "pdf_processing_results": [
                {
                    "filename": r.filename,
                    "status": r.status.value,
                    "processing_time": r.processing_time,
                    "reply_sent": r.reply_sent,
                    "error_message": r.error_message,
                    "metadata": {
                        "page_count": r.metadata.page_count if r.metadata else 0,
                        "file_size": r.metadata.file_size if r.metadata else 0,
                        "language": r.text_analysis.language if r.text_analysis else "unknown"
                    } if r.status == PDFProcessingStatus.SUCCESS else None
                }
                for r in processing_results
            ]
        }
