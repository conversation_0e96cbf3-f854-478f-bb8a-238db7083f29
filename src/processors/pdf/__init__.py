"""
PDF处理器模块

提供完整的PDF文件处理功能，包括文本提取、元数据分析、内容分析等。

主要组件：
- PdfProcessor: 主处理器类
- PDFMetadataExtractor: 元数据提取器
- PDFTextExtractor: 文本提取器
- PDFTextAnalyzer: 文本分析器

数据模型：
- PDFMetadata: PDF元数据
- PDFTextAnalysis: 文本分析结果
- PDFProcessingResult: 处理结果

异常类：
- PDFProcessingError: 基础异常
- PDFFileTooLargeError: 文件过大异常
- PDFCorruptedError: 文件损坏异常
- PDFEncryptedError: 文件加密异常
- PDFTextExtractionError: 文本提取异常
- PDFMemoryError: 内存不足异常

使用示例：
    from src.processors.pdf import PdfProcessor
    
    processor = PdfProcessor()
    result = processor.process(email_data, analysis_result)
"""

# 导入主要类和函数
from .processor import PdfProcessor
from .metadata_extractor import PDFMetadataExtractor
from .text_extractor import PDFTextExtractor
from .text_analyzer import PDFTextAnalyzer
from .warning_manager import PDFWarningManager, WarningCollector, suppress_pdf_warnings

# 导入数据模型
from .models import (
    PDFMetadata,
    PDFTextAnalysis,
    PDFProcessingResult,
    PDFProcessingStatus,
    TextExtractionMethod
)

# 导入异常类
from .exceptions import (
    PDFProcessingError,
    PDFFileTooLargeError,
    PDFCorruptedError,
    PDFEncryptedError,
    PDFTextExtractionError,
    PDFMemoryError
)

# 导入验证器和工具函数
from .validators import (
    validate_pdf_file,
    is_pdf_attachment,
    MAX_PDF_SIZE,
    SUPPORTED_PDF_MIMES,
    PDF_EXTENSIONS
)

from .utils import (
    calculate_file_hash,
    generate_safe_filename,
    format_file_size,
    get_current_time,
    send_email_reply,
    safe_format_datetime
)

# 定义公共接口
__all__ = [
    # 主要类
    'PdfProcessor',
    'PDFMetadataExtractor',
    'PDFTextExtractor',
    'PDFTextAnalyzer',
    'PDFWarningManager',
    'WarningCollector',
    
    # 数据模型
    'PDFMetadata',
    'PDFTextAnalysis',
    'PDFProcessingResult',
    'PDFProcessingStatus',
    'TextExtractionMethod',
    
    # 异常类
    'PDFProcessingError',
    'PDFFileTooLargeError',
    'PDFCorruptedError',
    'PDFEncryptedError',
    'PDFTextExtractionError',
    'PDFMemoryError',
    
    # 验证器和工具
    'validate_pdf_file',
    'is_pdf_attachment',
    'calculate_file_hash',
    'generate_safe_filename',
    'format_file_size',
    'get_current_time',
    'send_email_reply',
    'safe_format_datetime',
    'suppress_pdf_warnings',
    
    # 常量
    'MAX_PDF_SIZE',
    'SUPPORTED_PDF_MIMES',
    'PDF_EXTENSIONS'
]

# 版本信息
__version__ = '2.0.0'
__author__ = 'Mailer Project Team'
__description__ = 'Advanced PDF processing module for email automation'
