"""
邮件线程上下文处理器
"""
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import uuid
from loguru import logger

from ..core.thread_db import ThreadDB, DatabaseError
from ..core.context_engine import ContextEngine
from ..core.models.thread_models import Thread, Message, Clarification, ThreadStatus
from ..core.ai_analyzer import AnalysisResult
from .example_processor import BaseProcessor

class ThreadContextProcessor(BaseProcessor):
    """线程上下文处理器"""

    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化处理器
        
        Args:
            name: 处理器名称
            config: 配置字典
        """
        super().__init__(name, config or {})
        
        # 初始化数据库和上下文引擎
        db_path = self.config.get('thread_db_path', 'data/threads.db')
        self.thread_db = ThreadDB(db_path)
        self.context_engine = ContextEngine(self.thread_db)
        
        # 配置参数
        self.context_strategy = self.config.get('context_strategy', 'summary')
        self.clarification_timeout = self.config.get('clarification_timeout', 24)  # 小时
        
    def _extract_thread_id(self, email_data: Dict[str, Any]) -> str:
        """
        提取或生成线程ID
        
        Args:
            email_data: 邮件数据
            
        Returns:
            线程ID
        """
        # 从邮件头中提取现有线程ID
        headers = email_data.get('headers', {})
        thread_id = headers.get('Thread-ID') or headers.get('In-Reply-To')
        
        if not thread_id:
            # 生成新线程ID
            thread_id = f"thread_{uuid.uuid4().hex[:12]}"
            logger.info(f"生成新线程ID: {thread_id}")
            
        return thread_id

    def _create_thread(self, thread_id: str, email_data: Dict[str, Any]) -> Thread:
        """
        创建新线程
        
        Args:
            thread_id: 线程ID
            email_data: 邮件数据
            
        Returns:
            Thread对象
        """
        subject = email_data.get('subject', 'No Subject')
        thread = Thread(
            thread_id=thread_id,
            subject=subject,
            status=ThreadStatus.ACTIVE
        )
        self.thread_db.create_thread(thread)
        logger.info(f"创建新线程: {thread_id}")
        return thread

    def _add_message(self, thread_id: str, email_data: Dict[str, Any]) -> Message:
        """
        添加消息到线程
        
        Args:
            thread_id: 线程ID
            email_data: 邮件数据
            
        Returns:
            Message对象
        """
        message = Message(
            message_id=f"msg_{uuid.uuid4().hex[:12]}",
            thread_id=thread_id,
            content=email_data.get('content', ''),
            metadata={
                'from': email_data.get('from'),
                'to': email_data.get('to'),
                'timestamp': email_data.get('date')
            }
        )
        self.thread_db.add_message(message)
        logger.info(f"添加消息到线程: {thread_id}")
        return message

    def _create_clarification_session(
        self, thread_id: str, missing_info: Dict[str, Any]
    ) -> Clarification:
        """
        创建澄清会话
        
        Args:
            thread_id: 线程ID
            missing_info: 缺失信息描述
            
        Returns:
            Clarification对象
        """
        expires_at = datetime.now() + timedelta(hours=self.clarification_timeout)
        clarification = Clarification(
            session_id=f"clarify_{uuid.uuid4().hex[:12]}",
            thread_id=thread_id,
            missing_info=missing_info,
            expires_at=expires_at
        )
        self.thread_db.create_clarification(clarification)
        logger.info(f"创建澄清会话: {clarification.session_id}")
        return clarification

    def _enrich_email_data(
        self, email_data: Dict[str, Any], context: str
    ) -> Dict[str, Any]:
        """
        使用上下文信息丰富邮件数据
        
        Args:
            email_data: 原始邮件数据
            context: 构建的上下文
            
        Returns:
            更新后的邮件数据
        """
        enriched_data = email_data.copy()
        enriched_data['thread_context'] = context
        return enriched_data

    def _needs_clarification(
        self, email_data: Dict[str, Any], analysis: AnalysisResult
    ) -> Optional[Dict[str, Any]]:
        """
        判断是否需要澄清
        
        Args:
            email_data: 邮件数据
            analysis: AI分析结果
            
        Returns:
            需要澄清的信息描述，不需要则返回None
        """
        # 根据AI分析结果判断是否需要澄清
        if analysis.action_required and analysis.confidence < 0.8:
            return {
                'reason': '需要更多上下文信息',
                'questions': analysis.processor_suggestions
            }
        return None

    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """
        判断是否可以处理该邮件
        
        Args:
            email_data: 邮件数据
            analysis: AI分析结果
            
        Returns:
            bool: 是否可以处理
        """
        # 检查是否包含必要的邮件信息
        if not email_data.get('content'):
            logger.warning("邮件缺少内容")
            return False
            
        # 检查是否需要上下文处理
        if analysis.action_required or 'In-Reply-To' in email_data.get('headers', {}):
            return True
            
        return False

    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """
        处理邮件
        
        Args:
            email_data: 邮件数据
            analysis: AI分析结果
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 1. 提取或生成线程ID
            thread_id = self._extract_thread_id(email_data)
            
            # 2. 获取或创建线程
            thread = self.thread_db.get_thread(thread_id)
            if not thread:
                thread = self._create_thread(thread_id, email_data)
            
            # 3. 添加消息到线程
            message = self._add_message(thread_id, email_data)
            
            # 4. 检查是否需要澄清
            missing_info = self._needs_clarification(email_data, analysis)
            if missing_info:
                clarification = self._create_clarification_session(
                    thread_id, missing_info
                )
                # 更新邮件数据，标记需要澄清
                email_data['clarification_session_id'] = clarification.session_id
                logger.info(f"邮件需要澄清: {clarification.session_id}")
                return {
                    'success': True,
                    'needs_clarification': True,
                    'email_data': email_data,
                    'clarification': missing_info
                }
            
            # 5. 构建上下文
            context = self.context_engine.build_context(
                thread_id, self.context_strategy
            )
            
            # 6. 丰富邮件数据
            enriched_data = self._enrich_email_data(email_data, context)
            
            # 7. 清理过期会话
            self.thread_db.cleanup_expired_sessions()
            
            logger.info(f"邮件处理完成: {thread_id}")
            return {
                'success': True,
                'needs_clarification': False,
                'email_data': enriched_data
            }
            
        except DatabaseError as e:
            logger.error(f"数据库操作失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': "数据库操作失败"
            }
        except Exception as e:
            logger.error(f"处理邮件失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': "处理过程中发生错误"
            }

    def cleanup(self) -> None:
        """清理资源"""
        try:
            self.thread_db.cleanup_expired_sessions()
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

    def validate_config(self) -> bool:
        """
        验证配置
        
        Returns:
            bool: 配置是否有效
        """
        # 验证上下文策略
        valid_strategies = ['full', 'summary', 'key_points']
        if self.context_strategy not in valid_strategies:
            logger.error(f"无效的上下文策略: {self.context_strategy}")
            return False
            
        # 验证超时时间
        if not isinstance(self.clarification_timeout, (int, float)) or self.clarification_timeout <= 0:
            logger.error(f"无效的超时时间: {self.clarification_timeout}")
            return False
            
        return True

    def get_priority(self) -> int:
        """
        获取处理器优先级
        
        Returns:
            int: 优先级数字（越小优先级越高）
        """
        return 20  # 较高优先级，因为需要在其他处理前构建上下文