"""
智谱AI Web搜索客户端
负责与 zhipu-web-search-sse 服务通信

提供完整的智谱搜索功能，包括：
- SSE (Server-Sent Events) 连接
- HTTP/HTTPS 请求处理
- 自动重连机制
- 错误处理和回退
- 智谱AI官方API集成
"""

import json
import asyncio
import time
import threading
import queue
from typing import Dict, Any, List, Optional, Union, Callable, TypeVar, Protocol
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger
from contextlib import asynccontextmanager
from cachetools import TTLCache
from bs4 import BeautifulSoup, Tag
from bs4.element import NavigableString, PageElement
from types import SimpleNamespace

# 智谱API相关的协议定义
class ChatMessage(Protocol):
    """智谱消息协议"""
    content: Optional[str]
    tool_calls: Optional[List[Any]]
    
class ChatChoice(Protocol):
    """智谱选择协议"""
    message: ChatMessage
    
class ChatResponse(Protocol):
    """智谱AI聊天响应协议"""
    choices: List[ChatChoice]
    web_search: Optional[List[Dict[str, Any]]]

# 导入智谱AI相关库
ASYNC_LIBS_AVAILABLE = False
try:
    import httpx
    from zhipuai import ZhipuAI
    ASYNC_LIBS_AVAILABLE = True
    logger.info("异步库加载成功 (httpx, zhipuai)")
except ImportError as e:
    httpx = None
    ZhipuAI = None
    logger.warning(f"异步库未安装，智谱搜索客户端功能受限: {e}")
    logger.info("请安装异步库: pip install httpx zhipuai")


class SearchError(Exception):
    """智谱搜索错误基类"""
    pass


class ConnectionError(SearchError):
    """连接错误"""
    pass


class SearchResponseError(SearchError):
    """响应解析错误"""
    pass


class ZhipuConnectionState(Enum):
    """智谱搜索连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"


@dataclass
class ZhipuSearchRequest:
    """智谱搜索请求数据结构"""
    id: str
    query: str
    params: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    retries: int = 0
    callback: Optional[Callable] = None


@dataclass
class ZhipuSearchResponse:
    """智谱搜索响应数据结构"""
    id: str
    results: List[Dict[str, Any]] = field(default_factory=list)
    error: Optional[Dict[str, Any]] = None
    timestamp: float = field(default_factory=time.time)
    total_results: int = 0
    search_time: float = 0.0


class ZhipuSearchClient:
    """智谱AI Web搜索客户端，使用官方SDK和Web Search工具"""

    # 类型别名
    ZhipuResponse = Union[ChatResponse, SimpleNamespace]

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化智谱搜索客户端

        Args:
            config: 智谱搜索配置
        """
        # 初始化基础配置
        self.config = config or {}
        
        # 获取MCP配置（支持两种配置路径）
        self.mcp_config = self.config.get('ai', {}).get('mcp', {})
        if not self.mcp_config:
            self.mcp_config = self.config.get('mcp', {})
            
        # 初始化核心配置
        self.api_key = self.mcp_config.get('api_key') or self.config.get('api_key', '')
        self.timeout = self.mcp_config.get('timeout', 30)
        self.max_retries = self.mcp_config.get('max_retries', 3)
        self.retry_delay = self.mcp_config.get('retry_delay', 1.0)

        # 智谱AI客户端
        self.zhipu_client = None
        self.connection_state = ZhipuConnectionState.DISCONNECTED

        # 请求管理
        self.request_id = 0
        self._pending_requests: Dict[str, ZhipuSearchRequest] = {}

        # 智谱搜索特定配置
        self.zhipu_config = self.mcp_config.get('zhipu_search', {})
        if not self.zhipu_config:
            self.zhipu_config = self.config.get('zhipu_search', {})
        
        # 结果缓存
        self._cache = TTLCache(
            maxsize=self.config.get('cache_size', 100),
            ttl=self.config.get('cache_ttl', 300)  # 5分钟缓存
        )

    def _build_search_params(self, query: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """构建智谱AI搜索参数"""
        default_params = {
            "model": "glm-4",
            "messages": [
                {
                    "role": "user",
                    "content": f"请搜索关于'{query}'的信息"
                }
            ],
            "tools": [{
                "type": "web_search",
                "web_search": {
                    "search_query": query,
                    "search_result": True,
                }
            }],
            "temperature": 0.1,
            "max_tokens": 1000
        }
        
        # 合并用户传入的参数
        if params:
            default_params.update(params)
            
        return default_params

    def _parse_search_response(self, response: ZhipuResponse) -> List[Dict[str, Any]]:
        """解析智谱AI搜索响应"""
        results = []
        
        try:
            # 从web_search字段解析
            web_search_data = getattr(response, 'web_search', None)
            if web_search_data:
                logger.debug(f"找到web_search字段，处理搜索结果")
                for result in web_search_data:
                    if isinstance(result, dict):
                        standardized = self._standardize_search_result(result)
                        if standardized:
                            results.append(standardized)
                return results

            # 从tool_calls解析
            if hasattr(response, 'choices') and response.choices:
                choice = response.choices[0]
                message = getattr(choice, 'message', None)
                if message and hasattr(message, 'tool_calls'):
                    for tool_call in message.tool_calls:
                        if getattr(tool_call, 'type', None) == "web_search":
                            web_search = getattr(tool_call, 'web_search', None)
                            if web_search and hasattr(web_search, 'results'):
                                for result in web_search.results:
                                    if isinstance(result, dict):
                                        standardized = self._standardize_search_result(result)
                                        if standardized:
                                            results.append(standardized)

            # 记录无结果情况
            if not results and hasattr(response, 'choices') and response.choices:
                content = getattr(response.choices[0].message, 'content', None)
                if content:
                    logger.debug(f"搜索响应内容（无结果）: {content[:200]}...")

        except Exception as e:
            logger.error(f"解析搜索响应失败: {e}")
            raise SearchResponseError(f"解析搜索响应失败: {e}")

        return results
        
    async def _ensure_connection(self) -> bool:
        """确保连接可用，包含重试逻辑"""
        retries = 0
        while retries < self.max_retries:
            if await self.connect():
                return True
            retries += 1
            wait_time = self.retry_delay * (2 ** (retries - 1))
            logger.warning(f"连接重试 {retries}/{self.max_retries}, 等待 {wait_time}秒")
            await asyncio.sleep(wait_time)
        return False

    
    async def connect(self) -> bool:
        """
        初始化智谱AI客户端

        Returns:
            bool: 连接是否成功
        """
        if not ASYNC_LIBS_AVAILABLE:
            logger.error("智谱AI SDK未安装，无法连接智谱搜索服务")
            logger.info("请安装智谱AI SDK: pip install zhipuai")
            return False

        if ZhipuAI is None:
            logger.error("ZhipuAI库未正确导入")
            return False

        if not self.api_key:
            logger.error("智谱AI API密钥未配置，请在配置文件的 ai.mcp.api_key 或 mcp.api_key 中设置")
            logger.debug(f"当前配置: {self.config}")
            return False

        self.connection_state = ZhipuConnectionState.CONNECTING

        try:
            logger.info("初始化智谱AI客户端...")

            # 创建智谱AI客户端
            self.zhipu_client = ZhipuAI(api_key=self.api_key)

            self.connection_state = ZhipuConnectionState.CONNECTED
            logger.info("智谱AI客户端初始化成功")

            return True

        except Exception as e:
            logger.error(f"智谱AI客户端初始化失败: {e}")
            self.connection_state = ZhipuConnectionState.ERROR
            return False

    async def disconnect(self):
        """断开智谱AI客户端连接"""
        self.connection_state = ZhipuConnectionState.DISCONNECTED

        # 清理智谱AI客户端
        if self.zhipu_client:
            try:
                self.zhipu_client = None
                logger.info("智谱AI客户端连接已断开")
            except Exception as e:
                logger.error(f"断开智谱AI客户端连接时出错: {e}")

        # 清理待处理的请求
        for request_id, request in self._pending_requests.items():
            logger.warning(f"清理未完成的请求: {request_id}")
        self._pending_requests.clear()
    
    async def _send_search_request(self, query: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用智谱AI Web Search工具执行搜索，包含重试机制

        Args:
            query: 搜索查询
            params: 搜索参数

        Returns:
            Dict[str, Any]: 搜索结果
        """
        # 检查缓存
        cache_key = f"{query}:{hash(frozenset(params.items()))}"
        if hasattr(self, '_cache') and cache_key in self._cache:
            logger.debug(f"从缓存返回搜索结果: {query}")
            return self._cache[cache_key]

        retries = 0
        last_error = None
        
        while retries <= self.max_retries:
            try:
                if self.connection_state != ZhipuConnectionState.CONNECTED:
                    if not await self._ensure_connection():
                        raise ConnectionError("无法连接到智谱AI服务")

                if not self.zhipu_client:
                    raise ConnectionError("智谱AI客户端未初始化")

                logger.debug(f"使用智谱AI Web Search工具搜索: {query}")
                search_start = time.time()

                # 构建搜索参数
                search_params = self._build_search_params(query, params)
                
                # 处理智谱AI的异步/同步调用
                completion_create = getattr(self.zhipu_client.chat.completions, 'create')
                if asyncio.iscoroutinefunction(completion_create):
                    response = await completion_create(**search_params)
                else:
                    # 如果是同步方法，在线程池中执行
                    loop = asyncio.get_event_loop()
                    response = await loop.run_in_executor(
                        None,
                        lambda: completion_create(**search_params)
                    )

                # 解析响应
                results = self._parse_search_response(response)
                search_time = time.time() - search_start
                
                result = {
                    'results': results,
                    'search_time': search_time
                }

                # 缓存结果
                if hasattr(self, '_cache'):
                    self._cache[cache_key] = result

                logger.debug(f"智谱AI搜索完成，找到 {len(results)} 个结果，耗时: {search_time:.2f}秒")
                return result

            except Exception as e:
                last_error = e
                retries += 1
                if retries > self.max_retries:
                    logger.error(f"智谱AI搜索在 {self.max_retries} 次重试后仍然失败: {e}")
                    raise SearchError(f"搜索请求失败: {str(e)}") from e

                wait_time = self.retry_delay * (2 ** (retries - 1))
                logger.warning(f"搜索请求失败(重试 {retries}/{self.max_retries}): {e}")
                logger.warning(f"等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)

        raise SearchError(f"搜索请求在最大重试次数后仍然失败: {str(last_error)}")
    
    async def search_web(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        使用智谱AI Web搜索执行网络搜索

        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
                - max_results: 最大结果数 (默认: 5)
                - language: 搜索语言 (默认: 'zh')
                - region: 搜索地区 (默认: 'CN')
                - search_type: 搜索类型 (默认: 'general')
                - time_range: 时间范围 (可选)
                - safe_search: 安全搜索 (默认: 'moderate')

        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        # 构建智谱搜索特定的参数
        params = {
            "num_results": kwargs.get('max_results', 5),
            "language": kwargs.get('language', 'zh'),
            "region": kwargs.get('region', 'CN'),
            "search_type": kwargs.get('search_type', 'general'),
            "safe_search": kwargs.get('safe_search', 'moderate')
        }

        # 添加可选参数
        if 'time_range' in kwargs:
            params['time_range'] = kwargs['time_range']

        # 合并智谱特定配置
        params.update(self.zhipu_config)

        try:
            # 使用智谱AI SSE API执行搜索
            result = await self._send_search_request(query, params)

            # 处理智谱搜索的响应格式
            if isinstance(result, dict):
                # 如果返回的是包装的结果
                search_results = result.get('results', result.get('data', []))
            elif isinstance(result, list):
                # 如果直接返回结果列表
                search_results = result
            else:
                logger.warning(f"未知的搜索结果格式: {type(result)}")
                return []

            # 标准化结果格式
            standardized_results = []
            for item in search_results:
                standardized_item = self._standardize_search_result(item)
                if standardized_item:
                    standardized_results.append(standardized_item)

            logger.info(f"智谱AI搜索完成，找到 {len(standardized_results)} 个结果")
            return standardized_results

        except Exception as e:
            logger.error(f"智谱AI搜索失败: {e}")
            return []

    def _standardize_search_result(self, raw_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        标准化搜索结果格式，适配智谱AI搜索的响应格式

        Args:
            raw_result: 原始搜索结果

        Returns:
            Optional[Dict[str, Any]]: 标准化后的搜索结果
        """
        try:
            # 处理智谱AI搜索的结果格式
            standardized = {
                'title': raw_result.get('title', ''),
                'url': raw_result.get('url', raw_result.get('link', '')),
                'snippet': raw_result.get('snippet', raw_result.get('description', raw_result.get('content', ''))),
                'source': raw_result.get('source', raw_result.get('domain', 'Unknown')),
                'publish_date': raw_result.get('publish_date', raw_result.get('date', None))
            }

            # 验证必需字段
            if not standardized['title'] or not standardized['url']:
                logger.debug(f"搜索结果缺少必需字段: {raw_result}")
                return None

            return standardized

        except Exception as e:
            logger.error(f"标准化搜索结果失败: {e}")
            return None
    
    def search_web_sync(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        同步版本的网络搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        if not ASYNC_LIBS_AVAILABLE:
            logger.error("异步库未安装，无法执行MCP搜索")
            return []
        
        try:
            # 创建新的事件循环或使用现有的
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果循环正在运行，创建新的任务
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(
                            lambda: asyncio.run(self.search_web(query, **kwargs))
                        )
                        return future.result(timeout=self.timeout)
                else:
                    return loop.run_until_complete(self.search_web(query, **kwargs))
            except RuntimeError:
                # 没有事件循环，创建新的
                return asyncio.run(self.search_web(query, **kwargs))
                
        except Exception as e:
            logger.error(f"同步网络搜索失败: {e}")
            return []
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
        return False


class FallbackWebSearcher:
    """备用网络搜索器（当MCP不可用时使用）"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化备用搜索器
        
        Args:
            config: 搜索配置
        """
        self.config = config or {}
        self.timeout = self.config.get('timeout', 30)
        self.user_agent = self.config.get(
            'user_agent', 
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
    
    def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        执行备用搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            import requests
            from bs4 import BeautifulSoup
            import urllib.parse
            
            # 构建搜索URL（使用DuckDuckGo作为备用）
            encoded_query = urllib.parse.quote_plus(query)
            search_url = f"https://duckduckgo.com/html/?q={encoded_query}"
            
            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            
            # 发送请求
            response = requests.get(search_url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析结果
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # 查找搜索结果
            result_elements = soup.find_all('div', attrs={'class': 'result'})
            max_results = kwargs.get('max_results', 5)
            
            for element in result_elements[:max_results]:
                if not isinstance(element, Tag):
                    continue
                    
                try:
                    # 提取标题
                    title_elem = element.find('a', attrs={'class': 'result__a'})
                    title = title_elem.get_text(strip=True) if isinstance(title_elem, Tag) else ''
                    
                    # 提取URL
                    url = title_elem.get('href', '') if isinstance(title_elem, Tag) else ''
                    
                    # 提取摘要
                    snippet_elem = element.find('a', attrs={'class': 'result__snippet'})
                    snippet = snippet_elem.get_text(strip=True) if isinstance(snippet_elem, Tag) else ''
                    
                    if title and url:
                        results.append({
                            'title': title,
                            'url': url,
                            'snippet': snippet,
                            'source': 'DuckDuckGo',
                            'publish_date': None
                        })
                        
                except Exception as e:
                    logger.debug(f"解析搜索结果项失败: {e}")
                    continue
            
            logger.info(f"备用搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"备用搜索失败: {e}")
            return []


# 向后兼容性别名
MCPClient = ZhipuSearchClient
MCPConnectionState = ZhipuConnectionState
MCPRequest = ZhipuSearchRequest
MCPResponse = ZhipuSearchResponse
