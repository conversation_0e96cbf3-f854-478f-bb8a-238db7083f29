"""
回复生成器组件
负责生成最终的邮件回复内容，集成AI服务
"""

import json
from typing import Dict, Any, List, Optional
from loguru import logger

from ..models.task_models import TaskResult, TaskType
from ..models.search_models import SearchContext


class ResponseGenerator:
    """回复生成器 - 生成最终的邮件回复内容"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化回复生成器
        
        Args:
            config: 生成器配置
        """
        self.config = config or {}
        
        # 生成配置
        self.max_reply_length = self.config.get('max_reply_length', 1000)
        self.include_sources = self.config.get('include_sources', True)
        self.professional_tone = self.config.get('professional_tone', True)
        self.language = self.config.get('language', 'zh')
        
        # AI配置
        self.ai_config = self.config.get('ai', {})
        self.ai_provider = None
        self._initialize_ai_provider()
    
    def _initialize_ai_provider(self):
        """初始化AI提供商"""
        try:
            from src.core.config_manager import ConfigManager
            from src.core.ai_analyzer import AIAnalyzer
            
            config_manager = ConfigManager()
            config = config_manager.load_config()
            self.ai_provider = AIAnalyzer(config.ai)
            
        except Exception as e:
            logger.warning(f"AI提供商初始化失败: {e}")
            self.ai_provider = None
    
    def generate_response(self, email_data: Dict[str, Any], 
                         task_result: TaskResult,
                         search_context: Optional[SearchContext] = None) -> str:
        """
        生成最终回复内容
        
        Args:
            email_data: 原始邮件数据
            task_result: 任务执行结果
            search_context: 搜索上下文（可选）
            
        Returns:
            str: 生成的回复内容
        """
        logger.info(f"开始生成回复 (任务类型: {task_result.task_type.value})")
        
        try:
            # 根据任务类型选择生成策略
            if task_result.task_type == TaskType.SIMPLE_REPLY:
                response = self._generate_simple_response(email_data, task_result)
            elif task_result.task_type == TaskType.SEARCH_AND_REPLY:
                response = self._generate_search_based_response(email_data, task_result, search_context)
            elif task_result.task_type == TaskType.MULTI_STEP_ANALYSIS:
                response = self._generate_analytical_response(email_data, task_result, search_context)
            elif task_result.task_type == TaskType.INFORMATION_GATHERING:
                response = self._generate_informative_response(email_data, task_result, search_context)
            elif task_result.task_type == TaskType.PROBLEM_SOLVING:
                response = self._generate_solution_response(email_data, task_result, search_context)
            elif task_result.task_type == TaskType.RESEARCH_TASK:
                response = self._generate_research_response(email_data, task_result, search_context)
            else:
                response = self._generate_fallback_response(email_data, task_result)
            
            # 后处理和优化
            final_response = self._post_process_response(response, email_data)
            
            logger.info("回复生成完成")
            return final_response
            
        except Exception as e:
            logger.error(f"回复生成失败: {e}")
            return self._generate_error_response(email_data, str(e))
    
    def _generate_simple_response(self, email_data: Dict[str, Any], 
                                task_result: TaskResult) -> str:
        """生成简单回复"""
        subject = email_data.get('subject', '')
        sender = email_data.get('sender', '')
        
        if self.ai_provider:
            # 使用AI生成回复
            prompt = self._build_simple_reply_prompt(email_data)
            return self._call_ai_service(prompt)
        else:
            # 使用模板回复
            return f"感谢您的邮件。关于您提到的 '{subject}' 问题，我已收到您的信息并会及时处理。如有任何疑问，请随时联系我。"
    
    def _generate_search_based_response(self, email_data: Dict[str, Any], 
                                      task_result: TaskResult,
                                      search_context: Optional[SearchContext]) -> str:
        """生成基于搜索的回复"""
        if not search_context or not search_context.results:
            return self._generate_simple_response(email_data, task_result)
        
        if self.ai_provider:
            prompt = self._build_search_based_prompt(email_data, search_context)
            return self._call_ai_service(prompt)
        else:
            return self._build_template_search_response(email_data, search_context)
    
    def _generate_analytical_response(self, email_data: Dict[str, Any], 
                                    task_result: TaskResult,
                                    search_context: Optional[SearchContext]) -> str:
        """生成分析性回复"""
        if self.ai_provider:
            prompt = self._build_analytical_prompt(email_data, task_result, search_context)
            return self._call_ai_service(prompt)
        else:
            return self._build_template_analytical_response(email_data, task_result)
    
    def _generate_informative_response(self, email_data: Dict[str, Any], 
                                     task_result: TaskResult,
                                     search_context: Optional[SearchContext]) -> str:
        """生成信息性回复"""
        return self._generate_search_based_response(email_data, task_result, search_context)
    
    def _generate_solution_response(self, email_data: Dict[str, Any], 
                                  task_result: TaskResult,
                                  search_context: Optional[SearchContext]) -> str:
        """生成解决方案回复"""
        if self.ai_provider:
            prompt = self._build_solution_prompt(email_data, task_result, search_context)
            return self._call_ai_service(prompt)
        else:
            return "感谢您提出的问题。我已经分析了您的情况，建议您尝试以下解决方案..."
    
    def _generate_research_response(self, email_data: Dict[str, Any], 
                                  task_result: TaskResult,
                                  search_context: Optional[SearchContext]) -> str:
        """生成研究性回复"""
        if self.ai_provider:
            prompt = self._build_research_prompt(email_data, task_result, search_context)
            return self._call_ai_service(prompt)
        else:
            return "基于我的研究和分析，为您提供以下详细信息和见解..."
    
    def _generate_fallback_response(self, email_data: Dict[str, Any], 
                                  task_result: TaskResult) -> str:
        """生成备用回复"""
        return "感谢您的邮件。我已收到您的信息，会仔细查看并尽快回复您。"
    
    def _generate_error_response(self, email_data: Dict[str, Any], error: str) -> str:
        """生成错误回复"""
        return "感谢您的邮件。由于技术原因，我暂时无法提供详细回复，但我已收到您的信息并会人工处理。"
    
    def _build_simple_reply_prompt(self, email_data: Dict[str, Any]) -> str:
        """构建简单回复的AI提示"""
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        
        return f"""
        请根据以下邮件内容生成一个专业、友好的回复：

        邮件主题: {subject}
        邮件内容: {body}

        回复要求：
        1. 语气要专业且友好
        2. 确认收到邮件
        3. 根据邮件内容提供有用的回复
        4. 如果需要进一步沟通，请提及
        5. 回复长度适中，不要过长

        请直接返回回复内容，不需要包含主题行或签名。
        """
    
    def _build_search_based_prompt(self, email_data: Dict[str, Any], 
                                 search_context: SearchContext) -> str:
        """构建基于搜索的AI提示"""
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        
        # 整理搜索结果
        search_summary = search_context.get_summary()
        top_results = search_context.get_top_results(3)
        
        sources_info = ""
        if self.include_sources and top_results:
            sources_info = "\n参考来源：\n"
            for i, result in enumerate(top_results, 1):
                sources_info += f"{i}. {result.title} - {result.source}\n"
        
        return f"""
        请根据以下邮件内容和搜索结果生成一个专业、详细的回复：

        邮件主题: {subject}
        邮件内容: {body}

        搜索结果摘要:
        {search_summary}

        回复要求：
        1. 基于搜索结果提供准确、有用的信息
        2. 语气要专业且友好
        3. 结构清晰，逻辑性强
        4. 如果搜索结果不完整，请说明
        5. 适当引用来源信息

        {sources_info}

        请生成一个综合性的回复，直接返回回复内容。
        """
    
    def _build_analytical_prompt(self, email_data: Dict[str, Any], 
                               task_result: TaskResult,
                               search_context: Optional[SearchContext]) -> str:
        """构建分析性AI提示"""
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        
        analysis_info = ""
        if search_context:
            analysis_info = f"\n搜索分析结果:\n{search_context.get_summary()}"
        
        return f"""
        请根据以下信息生成一个深入的分析性回复：

        邮件主题: {subject}
        邮件内容: {body}
        {analysis_info}

        回复要求：
        1. 提供深入的分析和见解
        2. 从多个角度考虑问题
        3. 结构化呈现分析结果
        4. 提供具体的建议或结论
        5. 保持客观和专业

        请生成一个分析性的回复，包含清晰的分析框架和结论。
        """
    
    def _build_solution_prompt(self, email_data: Dict[str, Any], 
                             task_result: TaskResult,
                             search_context: Optional[SearchContext]) -> str:
        """构建解决方案AI提示"""
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        
        solution_context = ""
        if search_context:
            solution_context = f"\n相关解决方案信息:\n{search_context.get_summary()}"
        
        return f"""
        请根据以下问题描述生成具体的解决方案：

        问题主题: {subject}
        问题描述: {body}
        {solution_context}

        回复要求：
        1. 识别核心问题
        2. 提供具体可行的解决步骤
        3. 考虑不同的解决方案选项
        4. 说明每个方案的优缺点
        5. 给出明确的建议

        请生成一个解决方案导向的回复，重点关注实用性和可操作性。
        """
    
    def _build_research_prompt(self, email_data: Dict[str, Any], 
                             task_result: TaskResult,
                             search_context: Optional[SearchContext]) -> str:
        """构建研究性AI提示"""
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        
        research_data = ""
        if search_context:
            research_data = f"\n研究数据:\n{search_context.get_summary()}"
        
        return f"""
        请根据以下研究请求生成详细的研究报告：

        研究主题: {subject}
        研究需求: {body}
        {research_data}

        回复要求：
        1. 提供全面的背景信息
        2. 分析当前趋势和发展
        3. 总结关键发现和洞察
        4. 提供数据支持的结论
        5. 建议进一步的研究方向

        请生成一个研究报告式的回复，内容要详实、有深度。
        """
    
    def _call_ai_service(self, prompt: str) -> str:
        """调用AI服务生成回复"""
        try:
            if not self.ai_provider:
                raise Exception("AI提供商未初始化")
            
            # 根据AI提供商类型调用相应的API
            provider = self.ai_provider.provider
            client = getattr(provider, 'client', None)
            
            if not client:
                raise Exception("AI客户端未初始化")
            
            if self.ai_provider.config.provider.lower() == "openai":
                response = client.chat.completions.create(
                    model=self.ai_provider.config.model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的邮件助手，负责生成高质量的邮件回复。"},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=self.max_reply_length,
                    temperature=0.7
                )
                return response.choices[0].message.content
                
            elif self.ai_provider.config.provider.lower() == "anthropic":
                response = client.messages.create(
                    model=self.ai_provider.config.model,
                    max_tokens=self.max_reply_length,
                    temperature=0.7,
                    messages=[
                        {"role": "user", "content": prompt}
                    ]
                )
                
                # 处理Anthropic响应
                if hasattr(response, 'content') and isinstance(response.content, list):
                    return response.content[0].text
                else:
                    return str(response.content)
            else:
                raise Exception(f"不支持的AI提供商: {self.ai_provider.config.provider}")
                
        except Exception as e:
            logger.error(f"AI服务调用失败: {e}")
            return "感谢您的邮件，我会仔细查看并尽快回复您。"
    
    def _build_template_search_response(self, email_data: Dict[str, Any], 
                                      search_context: SearchContext) -> str:
        """构建基于模板的搜索回复"""
        subject = email_data.get('subject', '')
        summary = search_context.get_summary()
        
        response_parts = [
            f"感谢您关于 '{subject}' 的咨询。",
            "",
            "根据我的搜索和分析，为您提供以下信息：",
            "",
            summary
        ]
        
        if self.include_sources:
            top_results = search_context.get_top_results(3)
            if top_results:
                response_parts.append("\n参考来源：")
                for i, result in enumerate(top_results, 1):
                    response_parts.append(f"{i}. {result.title} - {result.source}")
        
        response_parts.append("\n如需更多信息，请随时联系我。")
        
        return "\n".join(response_parts)
    
    def _build_template_analytical_response(self, email_data: Dict[str, Any], 
                                          task_result: TaskResult) -> str:
        """构建基于模板的分析回复"""
        subject = email_data.get('subject', '')
        
        return f"""
        感谢您关于 '{subject}' 的详细咨询。

        基于我的分析，我为您提供以下见解：

        1. 问题分析：您提出的问题涉及多个方面，需要综合考虑。

        2. 关键要点：
           - 需要考虑当前的市场环境
           - 技术发展趋势的影响
           - 相关政策和法规的变化

        3. 建议方案：
           - 建议采用分步骤的实施策略
           - 密切关注行业动态
           - 保持灵活性以应对变化

        如需更详细的分析或有其他问题，请随时联系我。
        """
    
    def _post_process_response(self, response: str, email_data: Dict[str, Any]) -> str:
        """后处理回复内容"""
        # 长度控制
        if len(response) > self.max_reply_length:
            response = response[:self.max_reply_length] + "..."
        
        # 添加礼貌用语
        if not response.strip().endswith(('。', '！', '？', '.', '!', '?')):
            response += "。"
        
        # 确保专业语调
        if self.professional_tone and not any(greeting in response for greeting in ['感谢', '谢谢', '您好']):
            response = "感谢您的邮件。" + response
        
        return response.strip()
