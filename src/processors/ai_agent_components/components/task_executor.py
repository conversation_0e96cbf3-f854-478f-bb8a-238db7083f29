"""
任务执行器组件
负责执行复杂的多步骤任务流程
"""

import uuid
import time
from typing import Dict, Any, List, Optional
from loguru import logger

from ..models.task_models import (
    TaskType, TaskStatus, TaskStep, TaskResult, TaskContext
)
from ..models.search_models import SearchType
from .search_handler import <PERSON>Handler
from .analysis_engine import AnalysisEngine


class TaskExecutor:
    """任务执行器 - 执行复杂的多步骤任务"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化任务执行器
        
        Args:
            config: 执行器配置
        """
        self.config = config or {}
        self.search_handler = SearchHandler(config.get('search', {}))
        self.analysis_engine = AnalysisEngine(config.get('analysis', {}))
        
        # 执行配置
        self.max_execution_time = self.config.get('max_execution_time', 300)  # 5分钟
        self.enable_parallel_execution = self.config.get('enable_parallel_execution', False)
        self.retry_failed_steps = self.config.get('retry_failed_steps', True)
        self.max_retries = self.config.get('max_retries', 2)
    
    def execute_task(self, task_context: TaskContext) -> TaskResult:
        """
        执行任务
        
        Args:
            task_context: 任务上下文
            
        Returns:
            TaskResult: 任务执行结果
        """
        start_time = time.time()
        logger.info(f"开始执行任务: {task_context.task_id} (类型: {task_context.task_type.value})")
        
        try:
            # 根据任务类型创建执行步骤
            if not task_context.steps:
                self._create_task_steps(task_context)
            
            # 执行任务步骤
            executed_steps = self._execute_steps(task_context)
            
            # 生成最终结果
            final_response = self._generate_final_response(task_context, executed_steps)
            
            # 计算总执行时间
            total_duration = time.time() - start_time
            
            # 计算置信度
            confidence_score = self._calculate_confidence(executed_steps)
            
            # 创建任务结果
            result = TaskResult(
                task_id=task_context.task_id,
                task_type=task_context.task_type,
                status=TaskStatus.COMPLETED,
                final_response=final_response,
                steps_executed=executed_steps,
                total_duration=total_duration,
                confidence_score=confidence_score,
                metadata={
                    "email_subject": task_context.email_data.get('subject', ''),
                    "execution_time": total_duration,
                    "steps_count": len(executed_steps)
                }
            )
            
            logger.info(f"任务执行完成: {task_context.task_id}, 耗时: {total_duration:.2f}秒")
            return result
            
        except Exception as e:
            error_msg = f"任务执行失败: {str(e)}"
            logger.error(error_msg)
            
            return TaskResult(
                task_id=task_context.task_id,
                task_type=task_context.task_type,
                status=TaskStatus.FAILED,
                final_response=f"抱歉，处理您的请求时遇到了问题: {error_msg}",
                steps_executed=task_context.steps,
                total_duration=time.time() - start_time,
                confidence_score=0.0,
                metadata={"error": error_msg}
            )
    
    def _create_task_steps(self, task_context: TaskContext):
        """根据任务类型创建执行步骤"""
        task_type = task_context.task_type
        
        if task_type == TaskType.SIMPLE_REPLY:
            self._create_simple_reply_steps(task_context)
        elif task_type == TaskType.SEARCH_AND_REPLY:
            self._create_search_reply_steps(task_context)
        elif task_type == TaskType.MULTI_STEP_ANALYSIS:
            self._create_multi_step_analysis_steps(task_context)
        elif task_type == TaskType.INFORMATION_GATHERING:
            self._create_information_gathering_steps(task_context)
        elif task_type == TaskType.PROBLEM_SOLVING:
            self._create_problem_solving_steps(task_context)
        elif task_type == TaskType.RESEARCH_TASK:
            self._create_research_task_steps(task_context)
        else:
            # 默认简单回复
            self._create_simple_reply_steps(task_context)
    
    def _create_simple_reply_steps(self, task_context: TaskContext):
        """创建简单回复步骤"""
        step = TaskStep(
            step_id=str(uuid.uuid4()),
            name="生成简单回复",
            description="基于邮件内容生成直接回复",
            action="generate_simple_reply",
            parameters={"email_data": task_context.email_data}
        )
        task_context.add_step(step)
    
    def _create_search_reply_steps(self, task_context: TaskContext):
        """创建搜索回复步骤"""
        # 步骤1: 提取搜索关键词
        step1 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="提取搜索关键词",
            description="从邮件内容中提取搜索关键词",
            action="extract_search_keywords",
            parameters={"email_data": task_context.email_data}
        )
        task_context.add_step(step1)
        
        # 步骤2: 执行网络搜索
        step2 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="执行网络搜索",
            description="使用提取的关键词进行网络搜索",
            action="perform_search",
            parameters={"search_type": SearchType.GENERAL}
        )
        task_context.add_step(step2)
        
        # 步骤3: 分析搜索结果
        step3 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="分析搜索结果",
            description="分析搜索结果并提取关键信息",
            action="analyze_search_results",
            parameters={}
        )
        task_context.add_step(step3)
        
        # 步骤4: 生成综合回复
        step4 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="生成综合回复",
            description="基于搜索结果和邮件内容生成回复",
            action="generate_comprehensive_reply",
            parameters={}
        )
        task_context.add_step(step4)
    
    def _create_multi_step_analysis_steps(self, task_context: TaskContext):
        """创建多步骤分析步骤"""
        # 步骤1: 意图分析
        step1 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="意图分析",
            description="深入分析邮件意图和需求",
            action="analyze_intent",
            parameters={"email_data": task_context.email_data}
        )
        task_context.add_step(step1)
        
        # 步骤2: 多维度搜索
        step2 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="多维度搜索",
            description="从多个角度进行搜索",
            action="multi_dimensional_search",
            parameters={}
        )
        task_context.add_step(step2)
        
        # 步骤3: 信息综合分析
        step3 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="信息综合分析",
            description="综合分析所有收集的信息",
            action="synthesize_information",
            parameters={}
        )
        task_context.add_step(step3)
        
        # 步骤4: 生成详细回复
        step4 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="生成详细回复",
            description="生成详细的分析回复",
            action="generate_detailed_reply",
            parameters={}
        )
        task_context.add_step(step4)
    
    def _create_information_gathering_steps(self, task_context: TaskContext):
        """创建信息收集步骤"""
        # 类似于搜索回复，但更注重信息的完整性
        self._create_search_reply_steps(task_context)
        
        # 添加额外的验证步骤
        verification_step = TaskStep(
            step_id=str(uuid.uuid4()),
            name="信息验证",
            description="验证收集信息的准确性和完整性",
            action="verify_information",
            parameters={}
        )
        task_context.add_step(verification_step)
    
    def _create_problem_solving_steps(self, task_context: TaskContext):
        """创建问题解决步骤"""
        # 步骤1: 问题识别
        step1 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="问题识别",
            description="识别和分析具体问题",
            action="identify_problem",
            parameters={"email_data": task_context.email_data}
        )
        task_context.add_step(step1)
        
        # 步骤2: 解决方案搜索
        step2 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="解决方案搜索",
            description="搜索相关的解决方案",
            action="search_solutions",
            parameters={"search_type": SearchType.TECHNICAL}
        )
        task_context.add_step(step2)
        
        # 步骤3: 方案评估
        step3 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="方案评估",
            description="评估不同解决方案的可行性",
            action="evaluate_solutions",
            parameters={}
        )
        task_context.add_step(step3)
        
        # 步骤4: 生成解决建议
        step4 = TaskStep(
            step_id=str(uuid.uuid4()),
            name="生成解决建议",
            description="生成具体的解决建议",
            action="generate_solution_reply",
            parameters={}
        )
        task_context.add_step(step4)
    
    def _create_research_task_steps(self, task_context: TaskContext):
        """创建研究任务步骤"""
        # 最复杂的任务类型，包含多个搜索和分析步骤
        self._create_multi_step_analysis_steps(task_context)
        
        # 添加额外的研究步骤
        research_step = TaskStep(
            step_id=str(uuid.uuid4()),
            name="深度研究",
            description="进行深度研究和分析",
            action="deep_research",
            parameters={}
        )
        task_context.add_step(research_step)
    
    def _execute_steps(self, task_context: TaskContext) -> List[TaskStep]:
        """执行任务步骤"""
        executed_steps = []
        
        for step in task_context.steps:
            try:
                logger.info(f"执行步骤: {step.name}")
                step.mark_started()
                
                # 执行具体步骤
                result = self._execute_single_step(step, task_context)
                step.mark_completed(result)
                
                # 更新上下文数据
                task_context.context_data[step.step_id] = result
                
                executed_steps.append(step)
                logger.info(f"步骤完成: {step.name}")
                
            except Exception as e:
                error_msg = f"步骤执行失败: {str(e)}"
                logger.error(error_msg)
                step.mark_failed(error_msg)
                executed_steps.append(step)
                
                # 根据配置决定是否继续执行
                if not self.retry_failed_steps:
                    break
        
        return executed_steps
    
    def _execute_single_step(self, step: TaskStep, task_context: TaskContext) -> Dict[str, Any]:
        """执行单个步骤"""
        action = step.action
        
        if action == "generate_simple_reply":
            return self._generate_simple_reply(step.parameters, task_context)
        elif action == "extract_search_keywords":
            return self._extract_search_keywords(step.parameters, task_context)
        elif action == "perform_search":
            return self._perform_search(step.parameters, task_context)
        elif action == "analyze_search_results":
            return self._analyze_search_results(step.parameters, task_context)
        elif action == "generate_comprehensive_reply":
            return self._generate_comprehensive_reply(step.parameters, task_context)
        elif action == "analyze_intent":
            return self._analyze_intent(step.parameters, task_context)
        elif action == "multi_dimensional_search":
            return self._multi_dimensional_search(step.parameters, task_context)
        elif action == "synthesize_information":
            return self._synthesize_information(step.parameters, task_context)
        elif action == "generate_detailed_reply":
            return self._generate_detailed_reply(step.parameters, task_context)
        else:
            return {"status": "unknown_action", "message": f"未知操作: {action}"}
    
    def _generate_simple_reply(self, parameters: Dict[str, Any], 
                             task_context: TaskContext) -> Dict[str, Any]:
        """生成简单回复"""
        email_data = parameters.get("email_data", task_context.email_data)
        
        # 简单的回复生成逻辑
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        
        reply = f"感谢您的邮件。关于您提到的 '{subject}' 问题，我已收到您的信息并会及时处理。"
        
        return {
            "status": "success",
            "reply_content": reply,
            "reply_type": "simple"
        }
    
    def _extract_search_keywords(self, parameters: Dict[str, Any], 
                               task_context: TaskContext) -> Dict[str, Any]:
        """提取搜索关键词"""
        email_data = parameters.get("email_data", task_context.email_data)
        
        # 使用分析引擎提取关键词
        intent_result = self.analysis_engine.analyze_email_intent(email_data)
        
        return {
            "status": "success",
            "keywords": intent_result.keywords,
            "primary_query": " ".join(intent_result.keywords[:3])
        }
    
    def _perform_search(self, parameters: Dict[str, Any], 
                       task_context: TaskContext) -> Dict[str, Any]:
        """执行搜索"""
        # 从前一步获取关键词
        keywords_result = task_context.context_data.get(task_context.steps[0].step_id, {})
        query = keywords_result.get("primary_query", "")
        
        if not query:
            return {"status": "error", "message": "未找到搜索关键词"}
        
        search_type = parameters.get("search_type", SearchType.GENERAL)
        search_context = self.search_handler.search(query, search_type)
        
        return {
            "status": "success",
            "search_context": search_context,
            "results_count": len(search_context.results)
        }
    
    def _analyze_search_results(self, parameters: Dict[str, Any], 
                              task_context: TaskContext) -> Dict[str, Any]:
        """分析搜索结果"""
        # 获取搜索结果
        search_result = None
        for step_id, result in task_context.context_data.items():
            if result.get("search_context"):
                search_result = result
                break
        
        if not search_result:
            return {"status": "error", "message": "未找到搜索结果"}
        
        search_context = search_result["search_context"]
        intent_result = self.analysis_engine.analyze_email_intent(task_context.email_data)
        
        analysis = self.analysis_engine.analyze_search_results(search_context, intent_result)
        
        return {
            "status": "success",
            "analysis": analysis
        }
    
    def _generate_comprehensive_reply(self, parameters: Dict[str, Any], 
                                    task_context: TaskContext) -> Dict[str, Any]:
        """生成综合回复"""
        # 收集所有分析结果
        search_analysis = None
        for step_id, result in task_context.context_data.items():
            if result.get("analysis"):
                search_analysis = result["analysis"]
                break
        
        if not search_analysis:
            return self._generate_simple_reply(parameters, task_context)
        
        # 生成基于搜索结果的回复
        summary = search_analysis.get("summary", "")
        key_points = search_analysis.get("key_points", [])
        
        reply_parts = [
            "感谢您的咨询。根据我的搜索和分析，为您提供以下信息：",
            "",
            summary
        ]
        
        if key_points:
            reply_parts.append("\n关键要点：")
            for i, point in enumerate(key_points[:5], 1):
                reply_parts.append(f"{i}. {point}")
        
        reply = "\n".join(reply_parts)
        
        return {
            "status": "success",
            "reply_content": reply,
            "reply_type": "comprehensive"
        }
    
    def _analyze_intent(self, parameters: Dict[str, Any], 
                       task_context: TaskContext) -> Dict[str, Any]:
        """分析意图"""
        intent_result = self.analysis_engine.analyze_email_intent(task_context.email_data)
        
        return {
            "status": "success",
            "intent_result": intent_result
        }
    
    def _multi_dimensional_search(self, parameters: Dict[str, Any], 
                                task_context: TaskContext) -> Dict[str, Any]:
        """多维度搜索"""
        # 获取意图分析结果
        intent_result = None
        for step_id, result in task_context.context_data.items():
            if result.get("intent_result"):
                intent_result = result["intent_result"]
                break
        
        if not intent_result:
            return {"status": "error", "message": "未找到意图分析结果"}
        
        # 执行多个搜索查询
        queries = intent_result.keywords[:3]  # 使用前3个关键词
        search_contexts = self.search_handler.search_multiple_queries(queries)
        
        return {
            "status": "success",
            "search_contexts": search_contexts,
            "total_results": sum(len(ctx.results) for ctx in search_contexts)
        }
    
    def _synthesize_information(self, parameters: Dict[str, Any], 
                              task_context: TaskContext) -> Dict[str, Any]:
        """综合信息"""
        # 收集所有搜索结果进行综合分析
        all_results = []
        for step_id, result in task_context.context_data.items():
            if result.get("search_contexts"):
                for ctx in result["search_contexts"]:
                    all_results.extend(ctx.results)
        
        # 简单的信息综合
        synthesis = {
            "total_sources": len(all_results),
            "key_themes": [],  # 实际应用中应进行主题提取
            "confidence": 0.8 if all_results else 0.0
        }
        
        return {
            "status": "success",
            "synthesis": synthesis
        }
    
    def _generate_detailed_reply(self, parameters: Dict[str, Any], 
                               task_context: TaskContext) -> Dict[str, Any]:
        """生成详细回复"""
        # 基于综合分析生成详细回复
        reply = "基于详细的分析和研究，为您提供以下综合信息和建议..."
        
        return {
            "status": "success",
            "reply_content": reply,
            "reply_type": "detailed"
        }
    
    def _generate_final_response(self, task_context: TaskContext, 
                               executed_steps: List[TaskStep]) -> str:
        """生成最终回复"""
        # 从执行步骤中提取最终回复
        for step in reversed(executed_steps):
            if step.result and step.result.get("reply_content"):
                return step.result["reply_content"]
        
        # 如果没有找到回复内容，生成默认回复
        return "感谢您的邮件，我已收到并会尽快处理。"
    
    def _calculate_confidence(self, executed_steps: List[TaskStep]) -> float:
        """计算执行置信度"""
        if not executed_steps:
            return 0.0
        
        successful_steps = [step for step in executed_steps if step.status == TaskStatus.COMPLETED]
        return len(successful_steps) / len(executed_steps)
