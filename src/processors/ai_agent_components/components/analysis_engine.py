"""
分析引擎组件
负责分析邮件内容和搜索结果，提取关键信息
"""

import json
from typing import Dict, Any, List, Optional, Tuple
from loguru import logger

from ..models.search_models import SearchContext, SearchResult
from ..models.task_models import TaskType, TaskContext
from ..utils.intent_classifier import IntentClassifier, IntentResult


class AnalysisEngine:
    """分析引擎 - 分析邮件内容和搜索结果"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化分析引擎
        
        Args:
            config: 分析配置
        """
        self.config = config or {}
        self.intent_classifier = IntentClassifier()
        
        # 分析配置
        self.max_content_length = self.config.get('max_content_length', 2000)
        self.min_confidence_threshold = self.config.get('min_confidence_threshold', 0.6)
        self.enable_deep_analysis = self.config.get('enable_deep_analysis', True)
    
    def analyze_email_intent(self, email_data: Dict[str, Any]) -> IntentResult:
        """
        分析邮件意图
        
        Args:
            email_data: 邮件数据
            
        Returns:
            IntentResult: 意图分析结果
        """
        logger.info("开始分析邮件意图")
        
        try:
            # 使用意图分类器分析
            intent_result = self.intent_classifier.classify_intent(email_data)
            
            logger.info(f"意图分析完成: {intent_result.intent_type.value} "
                       f"(置信度: {intent_result.confidence:.2f})")
            
            return intent_result
            
        except Exception as e:
            logger.error(f"邮件意图分析失败: {e}")
            # 返回默认结果
            from ..utils.intent_classifier import IntentType
            from ..models.task_models import TaskType
            
            return IntentResult(
                intent_type=IntentType.UNKNOWN,
                confidence=0.0,
                task_type=TaskType.SIMPLE_REPLY,
                requires_search=False,
                keywords=[],
                complexity_score=0.0,
                reasoning=f"分析失败: {str(e)}"
            )
    
    def analyze_search_results(self, search_context: SearchContext, 
                             query_intent: IntentResult) -> Dict[str, Any]:
        """
        分析搜索结果
        
        Args:
            search_context: 搜索上下文
            query_intent: 查询意图
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        logger.info(f"开始分析搜索结果: {len(search_context.results)} 个结果")
        
        try:
            if not search_context.results:
                return {
                    "status": "no_results",
                    "summary": "未找到相关搜索结果",
                    "key_points": [],
                    "confidence": 0.0,
                    "recommendations": ["建议重新调整搜索关键词"]
                }
            
            # 提取关键信息
            key_points = self._extract_key_points(search_context.results)
            
            # 生成摘要
            summary = self._generate_summary(search_context.results, query_intent)
            
            # 评估信息质量
            quality_score = self._evaluate_information_quality(search_context.results)
            
            # 生成建议
            recommendations = self._generate_recommendations(search_context, query_intent)
            
            analysis_result = {
                "status": "success",
                "summary": summary,
                "key_points": key_points,
                "confidence": quality_score,
                "total_sources": len(search_context.results),
                "top_sources": [result.source for result in search_context.get_top_results(3)],
                "recommendations": recommendations,
                "search_time": search_context.search_time
            }
            
            logger.info(f"搜索结果分析完成，质量评分: {quality_score:.2f}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"搜索结果分析失败: {e}")
            return {
                "status": "error",
                "summary": f"分析过程中出现错误: {str(e)}",
                "key_points": [],
                "confidence": 0.0,
                "recommendations": ["建议重新搜索"]
            }
    
    def synthesize_information(self, email_data: Dict[str, Any], 
                             search_analysis: Dict[str, Any],
                             intent_result: IntentResult) -> Dict[str, Any]:
        """
        综合分析邮件和搜索信息
        
        Args:
            email_data: 邮件数据
            search_analysis: 搜索分析结果
            intent_result: 意图分析结果
            
        Returns:
            Dict[str, Any]: 综合分析结果
        """
        logger.info("开始综合信息分析")
        
        try:
            # 提取邮件关键信息
            email_summary = self._summarize_email(email_data)
            
            # 匹配搜索结果与邮件需求
            relevance_analysis = self._analyze_relevance(email_data, search_analysis)
            
            # 生成回复策略
            reply_strategy = self._generate_reply_strategy(intent_result, search_analysis)
            
            # 识别信息缺口
            information_gaps = self._identify_information_gaps(email_data, search_analysis)
            
            synthesis_result = {
                "email_summary": email_summary,
                "search_relevance": relevance_analysis,
                "reply_strategy": reply_strategy,
                "information_gaps": information_gaps,
                "confidence": min(intent_result.confidence, search_analysis.get('confidence', 0.0)),
                "complexity_level": intent_result.complexity_score,
                "recommended_approach": self._recommend_approach(intent_result, search_analysis)
            }
            
            logger.info("综合信息分析完成")
            return synthesis_result
            
        except Exception as e:
            logger.error(f"综合信息分析失败: {e}")
            return {
                "email_summary": "分析失败",
                "search_relevance": 0.0,
                "reply_strategy": "使用简单回复策略",
                "information_gaps": [f"分析错误: {str(e)}"],
                "confidence": 0.0,
                "complexity_level": 0.0,
                "recommended_approach": "fallback"
            }
    
    def _extract_key_points(self, results: List[SearchResult]) -> List[str]:
        """从搜索结果中提取关键点"""
        key_points = []
        
        for result in results[:5]:  # 只分析前5个结果
            # 从标题和摘要中提取关键信息
            title_points = self._extract_points_from_text(result.title)
            snippet_points = self._extract_points_from_text(result.snippet)
            
            key_points.extend(title_points)
            key_points.extend(snippet_points)
        
        # 去重并限制数量
        unique_points = list(set(key_points))
        return unique_points[:10]
    
    def _extract_points_from_text(self, text: str) -> List[str]:
        """从文本中提取关键点"""
        if not text:
            return []
        
        # 简单的关键点提取（实际应用中可以使用更复杂的NLP技术）
        sentences = text.split('。')
        points = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10 and len(sentence) < 100:
                points.append(sentence)
        
        return points[:3]  # 每个文本最多3个关键点
    
    def _generate_summary(self, results: List[SearchResult], intent: IntentResult) -> str:
        """生成搜索结果摘要"""
        if not results:
            return "未找到相关信息"
        
        # 根据意图类型生成不同风格的摘要
        top_results = results[:3]
        
        summary_parts = []
        for i, result in enumerate(top_results, 1):
            summary_parts.append(f"{i}. {result.title}: {result.snippet[:100]}...")
        
        return "\n".join(summary_parts)
    
    def _evaluate_information_quality(self, results: List[SearchResult]) -> float:
        """评估信息质量"""
        if not results:
            return 0.0
        
        total_score = 0.0
        for result in results:
            total_score += result.relevance_score
        
        return total_score / len(results)
    
    def _generate_recommendations(self, search_context: SearchContext, 
                                intent: IntentResult) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if search_context.total_results == 0:
            recommendations.append("建议调整搜索关键词")
            recommendations.append("尝试使用更通用的术语")
        elif search_context.total_results < 3:
            recommendations.append("搜索结果较少，建议扩大搜索范围")
        
        if intent.complexity_score > 0.7:
            recommendations.append("问题较复杂，建议提供详细分析")
        
        if intent.requires_search and not search_context.results:
            recommendations.append("需要搜索但未找到结果，建议人工介入")
        
        return recommendations
    
    def _summarize_email(self, email_data: Dict[str, Any]) -> str:
        """总结邮件内容"""
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        
        # 简单的邮件摘要
        if len(body) > 200:
            summary = body[:200] + "..."
        else:
            summary = body
        
        return f"主题: {subject}\n内容摘要: {summary}"
    
    def _analyze_relevance(self, email_data: Dict[str, Any], 
                          search_analysis: Dict[str, Any]) -> float:
        """分析搜索结果与邮件的相关性"""
        # 简单的相关性分析
        email_text = f"{email_data.get('subject', '')} {email_data.get('body', '')}"
        search_summary = search_analysis.get('summary', '')
        
        # 计算关键词重叠度
        email_words = set(email_text.lower().split())
        search_words = set(search_summary.lower().split())
        
        if not email_words:
            return 0.0
        
        overlap = email_words.intersection(search_words)
        return len(overlap) / len(email_words)
    
    def _generate_reply_strategy(self, intent: IntentResult, 
                               search_analysis: Dict[str, Any]) -> str:
        """生成回复策略"""
        if intent.task_type == TaskType.SIMPLE_REPLY:
            return "direct_answer"
        elif intent.requires_search and search_analysis.get('confidence', 0) > 0.7:
            return "search_based_comprehensive"
        elif intent.complexity_score > 0.6:
            return "detailed_analysis"
        else:
            return "standard_informative"
    
    def _identify_information_gaps(self, email_data: Dict[str, Any], 
                                 search_analysis: Dict[str, Any]) -> List[str]:
        """识别信息缺口"""
        gaps = []
        
        if search_analysis.get('confidence', 0) < 0.5:
            gaps.append("搜索结果质量不高")
        
        if search_analysis.get('total_sources', 0) < 2:
            gaps.append("信息来源不足")
        
        # 检查是否有未回答的问题
        email_body = email_data.get('body', '')
        question_count = email_body.count('?') + email_body.count('？')
        if question_count > len(search_analysis.get('key_points', [])):
            gaps.append("可能存在未充分回答的问题")
        
        return gaps
    
    def _recommend_approach(self, intent: IntentResult, 
                          search_analysis: Dict[str, Any]) -> str:
        """推荐处理方法"""
        confidence = min(intent.confidence, search_analysis.get('confidence', 0.0))
        
        if confidence > 0.8:
            return "automated_comprehensive"
        elif confidence > 0.6:
            return "automated_with_review"
        elif confidence > 0.4:
            return "assisted_manual"
        else:
            return "manual_review_required"
