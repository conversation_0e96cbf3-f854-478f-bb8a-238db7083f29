"""
意图识别工具
分析邮件内容，识别用户意图并决定处理策略
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from loguru import logger

from ..models.task_models import TaskType


class IntentType(Enum):
    """意图类型枚举"""
    SIMPLE_QUESTION = "simple_question"         # 简单问题
    COMPLEX_INQUIRY = "complex_inquiry"         # 复杂询问
    INFORMATION_REQUEST = "information_request" # 信息请求
    TECHNICAL_SUPPORT = "technical_support"     # 技术支持
    RESEARCH_REQUEST = "research_request"       # 研究请求
    GREETING = "greeting"                       # 问候
    COMPLAINT = "complaint"                     # 投诉
    FEEDBACK = "feedback"                       # 反馈
    MEETING_REQUEST = "meeting_request"         # 会议请求
    UNKNOWN = "unknown"                         # 未知意图


@dataclass
class IntentResult:
    """意图识别结果"""
    intent_type: IntentType                     # 识别的意图类型
    confidence: float                           # 置信度 (0-1)
    task_type: TaskType                         # 建议的任务类型
    requires_search: bool                       # 是否需要搜索
    keywords: List[str]                         # 关键词
    complexity_score: float                     # 复杂度评分 (0-1)
    reasoning: str                              # 识别理由


class IntentClassifier:
    """意图识别器"""
    
    def __init__(self):
        # 定义关键词模式
        self.patterns = {
            IntentType.SIMPLE_QUESTION: [
                r'什么是|是什么|怎么样|如何|为什么',
                r'请问|想知道|了解一下',
                r'\?|？'
            ],
            IntentType.COMPLEX_INQUIRY: [
                r'详细|具体|深入|全面|系统',
                r'分析|比较|评估|研究',
                r'方案|策略|建议|解决方案'
            ],
            IntentType.INFORMATION_REQUEST: [
                r'提供|发送|给我|需要',
                r'资料|文档|信息|数据',
                r'最新|更新|状态'
            ],
            IntentType.TECHNICAL_SUPPORT: [
                r'问题|故障|错误|bug',
                r'不能|无法|失败|异常',
                r'帮助|支持|解决|修复'
            ],
            IntentType.RESEARCH_REQUEST: [
                r'调研|研究|分析|报告',
                r'市场|行业|竞争|趋势',
                r'数据|统计|调查'
            ],
            IntentType.GREETING: [
                r'你好|您好|hello|hi',
                r'早上好|下午好|晚上好',
                r'感谢|谢谢|thank'
            ],
            IntentType.COMPLAINT: [
                r'投诉|抱怨|不满|问题',
                r'糟糕|差|不好|失望',
                r'退款|赔偿|解决'
            ],
            IntentType.FEEDBACK: [
                r'反馈|建议|意见|评价',
                r'改进|优化|提升',
                r'体验|感受|看法'
            ],
            IntentType.MEETING_REQUEST: [
                r'会议|开会|讨论|面谈',
                r'时间|安排|预约|约定',
                r'见面|沟通|交流'
            ]
        }
        
        # 搜索触发关键词
        self.search_triggers = [
            '最新', '新闻', '趋势', '市场', '价格', '比较',
            '评测', '排名', '推荐', '哪个好', '怎么选',
            '现状', '发展', '前景', '数据', '统计'
        ]
        
        # 复杂度指标
        self.complexity_indicators = [
            '详细分析', '深入研究', '全面评估', '系统梳理',
            '多角度', '综合考虑', '对比分析', '专业建议'
        ]
    
    def classify_intent(self, email_data: Dict[str, Any]) -> IntentResult:
        """
        分析邮件内容，识别用户意图
        
        Args:
            email_data: 邮件数据
            
        Returns:
            IntentResult: 意图识别结果
        """
        try:
            # 提取文本内容
            subject = email_data.get('subject', '')
            body = email_data.get('body', '')
            text_content = f"{subject} {body}".lower()
            
            # 识别意图
            intent_scores = self._calculate_intent_scores(text_content)
            best_intent, confidence = self._get_best_intent(intent_scores)
            
            # 判断是否需要搜索
            requires_search = self._requires_search(text_content)
            
            # 提取关键词
            keywords = self._extract_keywords(text_content)
            
            # 计算复杂度
            complexity_score = self._calculate_complexity(text_content)
            
            # 确定任务类型
            task_type = self._determine_task_type(best_intent, requires_search, complexity_score)
            
            # 生成识别理由
            reasoning = self._generate_reasoning(best_intent, confidence, requires_search, complexity_score)
            
            return IntentResult(
                intent_type=best_intent,
                confidence=confidence,
                task_type=task_type,
                requires_search=requires_search,
                keywords=keywords,
                complexity_score=complexity_score,
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error(f"意图识别失败: {e}")
            return IntentResult(
                intent_type=IntentType.UNKNOWN,
                confidence=0.0,
                task_type=TaskType.SIMPLE_REPLY,
                requires_search=False,
                keywords=[],
                complexity_score=0.0,
                reasoning=f"识别过程中出现错误: {str(e)}"
            )
    
    def _calculate_intent_scores(self, text: str) -> Dict[IntentType, float]:
        """计算各意图类型的匹配分数"""
        scores = {}
        
        for intent_type, patterns in self.patterns.items():
            score = 0.0
            matches = 0
            
            for pattern in patterns:
                if re.search(pattern, text):
                    matches += 1
                    score += 1.0
            
            # 归一化分数
            if patterns:
                scores[intent_type] = score / len(patterns)
            else:
                scores[intent_type] = 0.0
        
        return scores
    
    def _get_best_intent(self, scores: Dict[IntentType, float]) -> Tuple[IntentType, float]:
        """获取最佳意图匹配"""
        if not scores:
            return IntentType.UNKNOWN, 0.0
        
        best_intent = max(scores.keys(), key=lambda k: scores[k])
        confidence = scores[best_intent]
        
        # 如果置信度太低，返回未知意图
        if confidence < 0.3:
            return IntentType.UNKNOWN, confidence
        
        return best_intent, confidence
    
    def _requires_search(self, text: str) -> bool:
        """判断是否需要网络搜索"""
        for trigger in self.search_triggers:
            if trigger in text:
                return True
        return False
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取（实际应用中可以使用更复杂的NLP技术）
        keywords = []
        
        # 提取中文词汇（2-4个字符）
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,4}', text)
        keywords.extend(chinese_words[:10])  # 限制数量
        
        # 提取英文单词
        english_words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
        keywords.extend(english_words[:5])
        
        return list(set(keywords))  # 去重
    
    def _calculate_complexity(self, text: str) -> float:
        """计算内容复杂度"""
        complexity_score = 0.0
        
        # 基于复杂度指标
        for indicator in self.complexity_indicators:
            if indicator in text:
                complexity_score += 0.2
        
        # 基于文本长度
        if len(text) > 500:
            complexity_score += 0.3
        elif len(text) > 200:
            complexity_score += 0.1
        
        # 基于问号数量（多个问题表示复杂）
        question_count = text.count('?') + text.count('？')
        if question_count > 2:
            complexity_score += 0.2
        
        return min(complexity_score, 1.0)  # 限制在0-1范围内
    
    def _determine_task_type(self, intent: IntentType, requires_search: bool, complexity: float) -> TaskType:
        """根据意图确定任务类型"""
        if intent == IntentType.RESEARCH_REQUEST or complexity > 0.7:
            return TaskType.RESEARCH_TASK
        elif requires_search and complexity > 0.4:
            return TaskType.MULTI_STEP_ANALYSIS
        elif requires_search:
            return TaskType.SEARCH_AND_REPLY
        elif intent in [IntentType.INFORMATION_REQUEST, IntentType.COMPLEX_INQUIRY]:
            return TaskType.INFORMATION_GATHERING
        elif intent == IntentType.TECHNICAL_SUPPORT:
            return TaskType.PROBLEM_SOLVING
        else:
            return TaskType.SIMPLE_REPLY
    
    def _generate_reasoning(self, intent: IntentType, confidence: float, 
                          requires_search: bool, complexity: float) -> str:
        """生成识别理由"""
        reasoning_parts = [
            f"识别意图: {intent.value} (置信度: {confidence:.2f})",
            f"复杂度评分: {complexity:.2f}",
            f"需要搜索: {'是' if requires_search else '否'}"
        ]
        
        return " | ".join(reasoning_parts)
