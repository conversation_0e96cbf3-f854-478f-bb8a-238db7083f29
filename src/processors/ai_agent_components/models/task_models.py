"""
任务相关数据模型
定义复杂任务执行的数据结构
"""

from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime


class TaskType(Enum):
    """任务类型枚举"""
    SIMPLE_REPLY = "simple_reply"           # 简单回复
    SEARCH_AND_REPLY = "search_and_reply"   # 搜索后回复
    MULTI_STEP_ANALYSIS = "multi_step_analysis"  # 多步骤分析
    INFORMATION_GATHERING = "information_gathering"  # 信息收集
    PROBLEM_SOLVING = "problem_solving"     # 问题解决
    RESEARCH_TASK = "research_task"         # 研究任务


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"         # 待执行
    RUNNING = "running"         # 执行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 执行失败
    CANCELLED = "cancelled"     # 已取消


@dataclass
class TaskStep:
    """任务步骤数据模型"""
    step_id: str                        # 步骤ID
    name: str                          # 步骤名称
    description: str                   # 步骤描述
    action: str                        # 执行动作
    parameters: Dict[str, Any] = field(default_factory=dict)  # 参数
    status: TaskStatus = TaskStatus.PENDING  # 状态
    result: Optional[Dict[str, Any]] = None  # 执行结果
    error_message: Optional[str] = None      # 错误信息
    start_time: Optional[datetime] = None    # 开始时间
    end_time: Optional[datetime] = None      # 结束时间
    
    def mark_started(self):
        """标记步骤开始"""
        self.status = TaskStatus.RUNNING
        self.start_time = datetime.now()
    
    def mark_completed(self, result: Dict[str, Any]):
        """标记步骤完成"""
        self.status = TaskStatus.COMPLETED
        self.result = result
        self.end_time = datetime.now()
    
    def mark_failed(self, error: str):
        """标记步骤失败"""
        self.status = TaskStatus.FAILED
        self.error_message = error
        self.end_time = datetime.now()
    
    def get_duration(self) -> Optional[float]:
        """获取执行时长（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "step_id": self.step_id,
            "name": self.name,
            "description": self.description,
            "action": self.action,
            "parameters": self.parameters,
            "status": self.status.value,
            "result": self.result,
            "error_message": self.error_message,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration": self.get_duration()
        }


@dataclass
class TaskResult:
    """任务结果数据模型"""
    task_id: str                        # 任务ID
    task_type: TaskType                 # 任务类型
    status: TaskStatus                  # 最终状态
    final_response: str                 # 最终回复内容
    steps_executed: List[TaskStep]      # 执行的步骤
    total_duration: float = 0.0         # 总执行时长
    confidence_score: float = 0.0       # 置信度评分
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    
    def get_successful_steps(self) -> List[TaskStep]:
        """获取成功执行的步骤"""
        return [step for step in self.steps_executed if step.status == TaskStatus.COMPLETED]
    
    def get_failed_steps(self) -> List[TaskStep]:
        """获取失败的步骤"""
        return [step for step in self.steps_executed if step.status == TaskStatus.FAILED]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "task_type": self.task_type.value,
            "status": self.status.value,
            "final_response": self.final_response,
            "steps_executed": [step.to_dict() for step in self.steps_executed],
            "total_duration": self.total_duration,
            "confidence_score": self.confidence_score,
            "metadata": self.metadata,
            "successful_steps": len(self.get_successful_steps()),
            "failed_steps": len(self.get_failed_steps())
        }


@dataclass
class TaskContext:
    """任务上下文数据模型"""
    task_id: str                        # 任务ID
    task_type: TaskType                 # 任务类型
    email_data: Dict[str, Any]          # 原始邮件数据
    analysis_result: Any                # AI分析结果
    steps: List[TaskStep] = field(default_factory=list)  # 任务步骤
    current_step_index: int = 0         # 当前步骤索引
    context_data: Dict[str, Any] = field(default_factory=dict)  # 上下文数据
    created_at: datetime = field(default_factory=datetime.now)  # 创建时间
    
    def add_step(self, step: TaskStep):
        """添加任务步骤"""
        self.steps.append(step)
    
    def get_current_step(self) -> Optional[TaskStep]:
        """获取当前步骤"""
        if 0 <= self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None
    
    def move_to_next_step(self) -> bool:
        """移动到下一步骤"""
        if self.current_step_index < len(self.steps) - 1:
            self.current_step_index += 1
            return True
        return False
    
    def is_completed(self) -> bool:
        """检查任务是否完成"""
        return self.current_step_index >= len(self.steps)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "task_type": self.task_type.value,
            "email_data": self.email_data,
            "steps": [step.to_dict() for step in self.steps],
            "current_step_index": self.current_step_index,
            "context_data": self.context_data,
            "created_at": self.created_at.isoformat(),
            "is_completed": self.is_completed()
        }
