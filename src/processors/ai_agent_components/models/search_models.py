"""
搜索相关数据模型
定义网络搜索的请求、结果和上下文数据结构
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum


class SearchType(Enum):
    """搜索类型枚举"""
    GENERAL = "general"          # 通用搜索
    TECHNICAL = "technical"      # 技术搜索
    NEWS = "news"               # 新闻搜索
    ACADEMIC = "academic"       # 学术搜索
    PRODUCT = "product"         # 产品搜索


@dataclass
class SearchRequest:
    """搜索请求数据模型"""
    query: str                           # 搜索查询
    search_type: SearchType = SearchType.GENERAL  # 搜索类型
    max_results: int = 5                 # 最大结果数
    language: str = "zh"                 # 搜索语言
    region: str = "CN"                   # 搜索地区
    time_range: Optional[str] = None     # 时间范围 (如: "1d", "1w", "1m")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "query": self.query,
            "search_type": self.search_type.value,
            "max_results": self.max_results,
            "language": self.language,
            "region": self.region,
            "time_range": self.time_range
        }


@dataclass
class SearchResult:
    """单个搜索结果数据模型"""
    title: str                          # 标题
    url: str                           # URL
    snippet: str                       # 摘要
    source: str                        # 来源
    relevance_score: float = 0.0       # 相关性评分
    publish_date: Optional[str] = None  # 发布日期
    content: Optional[str] = None       # 完整内容（如果获取到）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "title": self.title,
            "url": self.url,
            "snippet": self.snippet,
            "source": self.source,
            "relevance_score": self.relevance_score,
            "publish_date": self.publish_date,
            "content": self.content
        }


@dataclass
class SearchContext:
    """搜索上下文数据模型"""
    request: SearchRequest              # 原始搜索请求
    results: List[SearchResult]         # 搜索结果列表
    total_results: int = 0              # 总结果数
    search_time: float = 0.0           # 搜索耗时（秒）
    error_message: Optional[str] = None # 错误信息
    
    def get_top_results(self, n: int = 3) -> List[SearchResult]:
        """获取前N个最相关的结果"""
        sorted_results = sorted(self.results, key=lambda x: x.relevance_score, reverse=True)
        return sorted_results[:n]
    
    def get_summary(self) -> str:
        """获取搜索结果摘要"""
        if not self.results:
            return "未找到相关搜索结果"
        
        summary_parts = []
        for i, result in enumerate(self.get_top_results(3), 1):
            summary_parts.append(f"{i}. {result.title}\n   {result.snippet}")
        
        return "\n\n".join(summary_parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "request": self.request.to_dict(),
            "results": [result.to_dict() for result in self.results],
            "total_results": self.total_results,
            "search_time": self.search_time,
            "error_message": self.error_message
        }
