"""
字符串处理工具函数
"""

import re
from email.header import decode_header
from typing import Optional


def decode_email_header(header_value: str) -> str:
    """
    解码邮件头中的编码字符串

    Args:
        header_value: 邮件头原始值，可能包含编码信息

    Returns:
        str: 解码后的字符串
    """
    if not header_value:
        return ""

    try:
        decoded = decode_header(header_value)
        result = ""
        for text, encoding in decoded:
            if isinstance(text, bytes):
                # 如果有指定编码，使用指定编码；否则尝试UTF-8
                try:
                    result += text.decode(encoding or 'utf-8')
                except (UnicodeDecodeError, LookupError):
                    # 如果解码失败，尝试其他常见编码
                    for fallback_encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                        try:
                            result += text.decode(fallback_encoding)
                            break
                        except (UnicodeDecodeError, LookupError):
                            continue
                    else:
                        # 所有编码都失败，使用错误处理
                        result += text.decode('utf-8', errors='replace')
            else:
                result += str(text)
        return result
    except Exception as e:
        # 如果解码完全失败，返回原始字符串
        return str(header_value)


def clean_text_for_notification(text: str) -> str:
    """
    清理文本用于通知显示，移除或替换可能导致问题的字符

    Args:
        text: 原始文本

    Returns:
        str: 清理后的文本
    """
    if not text:
        return ""

    # 首先解码邮件头
    cleaned = decode_email_header(text)

    # 移除控制字符（除了常见的空白字符）
    cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)

    # 替换可能导致问题的字符
    replacements = {
        '\r\n': ' ',
        '\r': ' ',
        '\n': ' ',
        '\t': ' ',
    }

    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)

    # 压缩多个空格为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)

    # 移除首尾空白
    cleaned = cleaned.strip()

    # 限制长度，避免通知过长
    if len(cleaned) > 100:
        cleaned = cleaned[:97] + "..."

    return cleaned


def escape_apple_script(text: str) -> str:
    """
    转义AppleScript字符串中的特殊字符

    Args:
        text: 原始文本

    Returns:
        str: 转义后的文本
    """
    if not text:
        return ""

    # 首先清理文本
    cleaned = clean_text_for_notification(text)

    # 转义AppleScript特殊字符
    # 注意：反斜杠必须首先转义
    escaped = cleaned.replace("\\", "\\\\")
    escaped = escaped.replace('"', '\\"')
    escaped = escaped.replace("'", "\\'")

    # 移除或转义其他可能导致问题的字符
    # AppleScript对某些Unicode字符敏感
    escaped = re.sub(r'[^\x20-\x7E\u4e00-\u9fff]', '', escaped)

    return escaped