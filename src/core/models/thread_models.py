"""
邮件线程相关数据模型
"""
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field

class ThreadStatus(str, Enum):
    """线程状态枚举"""
    ACTIVE = 'active'
    RESOLVED = 'resolved'
    EXPIRED = 'expired'

class Thread(BaseModel):
    """邮件线程模型"""
    thread_id: str = Field(..., description="线程唯一标识")
    subject: str = Field(..., description="邮件主题")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    status: ThreadStatus = Field(default=ThreadStatus.ACTIVE, description="线程状态")

    class Config:
        from_attributes = True

class Message(BaseModel):
    """邮件消息模型"""
    message_id: str = Field(..., description="消息唯一标识")
    thread_id: str = Field(..., description="所属线程ID")
    content: str = Field(..., description="消息内容")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="消息时间戳")

    class Config:
        from_attributes = True

class Clarification(BaseModel):
    """澄清会话模型"""
    session_id: str = Field(..., description="会话唯一标识")
    thread_id: str = Field(..., description="所属线程ID")
    missing_info: Dict[str, Any] = Field(..., description="缺失信息描述")
    expires_at: datetime = Field(..., description="过期时间")
    
    class Config:
        from_attributes = True