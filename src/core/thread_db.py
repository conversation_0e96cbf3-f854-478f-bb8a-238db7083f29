"""
线程数据库管理模块
"""
import sqlite3
from contextlib import contextmanager
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from loguru import logger
from threading import Lock

from .models.thread_models import Thread, Message, Clarification, ThreadStatus

class DatabaseError(Exception):
    """数据库操作异常"""
    pass

class ThreadDB:
    """线程数据库管理器"""
    
    def __init__(self, db_path: str = "data/threads.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._lock = Lock()
        self._init_db()
    
    def _init_db(self) -> None:
        """初始化数据库表"""
        try:
            with self._get_connection() as conn:
                conn.executescript("""
                    CREATE TABLE IF NOT EXISTS threads (
                        thread_id TEXT PRIMARY KEY,
                        subject TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        status TEXT CHECK(status IN ('active','resolved','expired'))
                    );

                    CREATE TABLE IF NOT EXISTS messages (
                        message_id TEXT PRIMARY KEY,
                        thread_id TEXT REFERENCES threads(thread_id),
                        content TEXT NOT NULL,
                        metadata JSON,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (thread_id) REFERENCES threads(thread_id) ON DELETE CASCADE
                    );

                    CREATE TABLE IF NOT EXISTS clarifications (
                        session_id TEXT PRIMARY KEY,
                        thread_id TEXT REFERENCES threads(thread_id),
                        missing_info JSON NOT NULL,
                        expires_at DATETIME NOT NULL,
                        FOREIGN KEY (thread_id) REFERENCES threads(thread_id) ON DELETE CASCADE
                    );
                """)
        except sqlite3.Error as e:
            raise DatabaseError(f"初始化数据库失败: {e}")

    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                yield conn
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise DatabaseError(f"数据库操作失败: {e}")
            finally:
                conn.close()

    def create_thread(self, thread: Thread) -> None:
        """
        创建新线程
        
        Args:
            thread: 线程数据模型
        """
        try:
            with self._get_connection() as conn:
                conn.execute(
                    "INSERT INTO threads (thread_id, subject, status) VALUES (?, ?, ?)",
                    (thread.thread_id, thread.subject, thread.status)
                )
                logger.info(f"创建线程成功: {thread.thread_id}")
        except sqlite3.IntegrityError:
            raise DatabaseError(f"线程ID已存在: {thread.thread_id}")

    def get_thread(self, thread_id: str) -> Optional[Thread]:
        """
        获取线程信息
        
        Args:
            thread_id: 线程ID
            
        Returns:
            Thread对象或None
        """
        try:
            with self._get_connection() as conn:
                result = conn.execute(
                    "SELECT * FROM threads WHERE thread_id = ?",
                    (thread_id,)
                ).fetchone()
                
                if result:
                    return Thread(**dict(result))
                return None
        except Exception as e:
            logger.error(f"获取线程失败: {e}")
            return None

    def add_message(self, message: Message) -> None:
        """
        添加消息到线程
        
        Args:
            message: 消息数据模型
        """
        try:
            with self._get_connection() as conn:
                conn.execute(
                    """INSERT INTO messages 
                       (message_id, thread_id, content, metadata, timestamp) 
                       VALUES (?, ?, ?, ?, ?)""",
                    (message.message_id, message.thread_id, message.content,
                     message.metadata, message.timestamp)
                )
                logger.info(f"添加消息成功: {message.message_id}")
        except sqlite3.IntegrityError as e:
            raise DatabaseError(f"添加消息失败: {e}")

    def get_thread_messages(self, thread_id: str) -> List[Message]:
        """
        获取线程的所有消息
        
        Args:
            thread_id: 线程ID
            
        Returns:
            消息列表
        """
        try:
            with self._get_connection() as conn:
                results = conn.execute(
                    "SELECT * FROM messages WHERE thread_id = ? ORDER BY timestamp",
                    (thread_id,)
                ).fetchall()
                return [Message(**dict(row)) for row in results]
        except Exception as e:
            logger.error(f"获取线程消息失败: {e}")
            return []

    def create_clarification(self, clarification: Clarification) -> None:
        """
        创建澄清会话
        
        Args:
            clarification: 澄清会话数据模型
        """
        try:
            with self._get_connection() as conn:
                conn.execute(
                    """INSERT INTO clarifications 
                       (session_id, thread_id, missing_info, expires_at) 
                       VALUES (?, ?, ?, ?)""",
                    (clarification.session_id, clarification.thread_id,
                     clarification.missing_info, clarification.expires_at)
                )
                logger.info(f"创建澄清会话成功: {clarification.session_id}")
        except sqlite3.IntegrityError as e:
            raise DatabaseError(f"创建澄清会话失败: {e}")

    def get_active_clarifications(self) -> List[Clarification]:
        """
        获取所有未过期的澄清会话
        
        Returns:
            澄清会话列表
        """
        try:
            with self._get_connection() as conn:
                now = datetime.now()
                results = conn.execute(
                    "SELECT * FROM clarifications WHERE expires_at > ?",
                    (now,)
                ).fetchall()
                return [Clarification(**dict(row)) for row in results]
        except Exception as e:
            logger.error(f"获取澄清会话失败: {e}")
            return []

    def cleanup_expired_sessions(self) -> int:
        """
        清理过期的澄清会话
        
        Returns:
            清理的会话数量
        """
        try:
            with self._get_connection() as conn:
                now = datetime.now()
                cur = conn.execute(
                    "DELETE FROM clarifications WHERE expires_at <= ?",
                    (now,)
                )
                count = cur.rowcount
                logger.info(f"清理过期会话完成: {count}个")
                return count
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            return 0

    def update_thread_status(self, thread_id: str, status: ThreadStatus) -> bool:
        """
        更新线程状态
        
        Args:
            thread_id: 线程ID
            status: 新状态
            
        Returns:
            是否更新成功
        """
        try:
            with self._get_connection() as conn:
                cur = conn.execute(
                    "UPDATE threads SET status = ? WHERE thread_id = ?",
                    (status, thread_id)
                )
                success = cur.rowcount > 0
                if success:
                    logger.info(f"更新线程状态成功: {thread_id} -> {status}")
                return success
        except Exception as e:
            logger.error(f"更新线程状态失败: {e}")
            return False