"""
处理器管理模块 - 负责管理和执行邮件处理器插件
"""

import importlib
import importlib.util
import os
import sys
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod
from loguru import logger
from .ai_analyzer import AnalysisResult


class BaseProcessor(ABC):
    """处理器基类"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.enabled = True
    
    @abstractmethod
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """判断是否可以处理该邮件"""
        pass
    
    @abstractmethod
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """处理邮件"""
        pass
    
    def get_priority(self) -> int:
        """获取处理器优先级（数字越小优先级越高）"""
        return 100
    
    def validate_config(self) -> bool:
        """验证配置"""
        return True


class ProcessorResult:
    """处理器执行结果"""
    
    def __init__(self, 
                 processor_name: str,
                 success: bool,
                 message: str = "",
                 data: Optional[Dict[str, Any]] = None,
                 error: Optional[Exception] = None):
        self.processor_name = processor_name
        self.success = success
        self.message = message
        self.data = data or {}
        self.error = error
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "processor_name": self.processor_name,
            "success": self.success,
            "message": self.message,
            "data": self.data,
            "error": str(self.error) if self.error else None
        }


class ProcessorManager:
    """处理器管理器"""
    
    def __init__(self, email_client, app_config: Dict[str, Any], processors_dir: str = "src/processors"):
        self.email_client = email_client
        self.app_config = app_config
        self.processors_dir = processors_dir
        self.processors: Dict[str, BaseProcessor] = {}
        self.load_processors()
    
    def load_processors(self):
        """加载所有处理器"""
        if not os.path.exists(self.processors_dir):
            logger.warning(f"处理器目录不存在: {self.processors_dir}")
            return
        
        # 加载内置处理器
        self._load_builtin_processors()
        
        # 加载自定义处理器
        self._load_custom_processors()
        
        logger.info(f"已加载 {len(self.processors)} 个处理器")
    
    def _load_builtin_processors(self):
        """加载内置处理器"""
        try:
            # 获取项目根目录并添加到sys.path
            base_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(base_dir))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)

            # 导入内置处理器
            from src.processors.log_processor import LogProcessor
            from src.processors.notification_processor import NotificationProcessor
            from src.processors.auto_reply_processor import AutoReplyProcessor
            from src.processors.pdf_processor import PdfProcessor  # 导入PDF处理器
            from src.processors.ai_reply_processor import AIReplyProcessor  # 导入AI回复处理器

            # 直接使用已有的应用配置
            config = self.app_config

            # 注册内置处理器
            self.register_processor(LogProcessor("log_processor", config.get('processors', {}).get('log_processor', {})))
            self.register_processor(NotificationProcessor("notification_processor", config.get('processors', {}).get('notification_processor', {})))
            self.register_processor(AutoReplyProcessor("auto_reply_processor", config.get('processors', {}).get('auto_reply_processor', {})))
            self.register_processor(PdfProcessor("pdf_processor", config.get('processors', {}).get('pdf_processor', {})))  # 注册PDF处理器
            self.register_processor(AIReplyProcessor("ai_reply_processor", config.get('processors', {}).get('ai_reply_processor', {})))  # 注册AI回复处理器

            logger.info("内置处理器加载完成")

        except ImportError as e:
            logger.warning(f"加载内置处理器失败: {e}")
            logger.debug(f"导入错误详情: {str(e)}", exc_info=True)
    
    def _load_custom_processors(self):
        """加载自定义处理器"""
        try:
            # 获取项目根目录
            base_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(base_dir))
            
            # 将项目根目录添加到sys.path
            sys.path.insert(0, project_root)
            
            # 忽略示例、测试和内置处理器
            builtin_processors = {
                'log_processor.py',
                'notification_processor.py',
                'auto_reply_processor.py',
                'pdf_processor.py',
                'ai_reply_processor.py'  # 新增AI回复处理器
            }
            ignore_files = {'example_processor.py', 'test_pdf_processor.py'} | builtin_processors
            
            for filename in os.listdir(self.processors_dir):
                if (filename.endswith('.py')
                    and not filename.startswith('__')
                    and filename not in ignore_files):
                    
                    module_name = filename[:-3]
                    try:
                        full_module_name = f"src.processors.{module_name}"
                        module = importlib.import_module(full_module_name)
                        
                        for attr_name in dir(module):
                            attr = getattr(module, attr_name)
                            if (isinstance(attr, type)
                                and issubclass(attr, BaseProcessor)
                                and attr != BaseProcessor):
                                
                                # 创建实例并注册
                                processor = attr(attr_name.lower())
                                if not self.register_processor(processor):
                                    logger.warning(f"跳过注册: {attr_name}")
                                
                    except Exception as e:
                        logger.error(f"加载处理器 {filename} 失败: {e}")
                        # 添加详细错误日志
                        logger.debug(f"错误详情: {str(e)}", exc_info=True)
            
            # 恢复sys.path
            sys.path.remove(project_root)
                        
        except Exception as e:
            logger.error(f"扫描处理器目录失败: {e}")
    
    def register_processor(self, processor: BaseProcessor):
        """注册处理器（增强唯一性和类型检查）"""
        # 检查名称唯一性
        if processor.name in self.processors:
            logger.warning(f"处理器 '{processor.name}' 已注册，跳过重复注册")
            return False
            
        # 验证处理器类型
        if not isinstance(processor, BaseProcessor):
            logger.error(f"无效处理器类型: {type(processor).__name__}")
            return False
            
        # 验证配置
        if not processor.validate_config():
            logger.error(f"处理器配置验证失败: {processor.name}")
            return False
            
        self.processors[processor.name] = processor
        logger.info(f"处理器已注册: {processor.name}")
        return True
    
    def unregister_processor(self, name: str):
        """注销处理器"""
        if name in self.processors:
            del self.processors[name]
            logger.info(f"处理器已注销: {name}")
    
    def get_processor(self, name: str) -> Optional[BaseProcessor]:
        """获取指定处理器"""
        return self.processors.get(name)
    
    def list_processors(self) -> List[str]:
        """列出所有处理器名称"""
        return list(self.processors.keys())
    
    def process_email(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> List[ProcessorResult]:
        """处理邮件"""
        results = []

        # 保存原始ID和文件夹信息用于标记已读
        # 使用UID而不是message_id
        email_id = email_data.get('uid')
        email_folder = email_data.get('folder', 'INBOX')

        # 优先使用AI建议的处理器
        if analysis.processor_suggestions:
            logger.info("使用AI建议的处理器")
            available_processors = self._get_ai_suggested_processors(email_data, analysis)
        else:
            logger.info("使用传统处理器选择逻辑")
            available_processors = self._get_traditional_processors(email_data, analysis)

        logger.info(f"找到 {len(available_processors)} 个可用处理器: {', '.join([p.name for p in available_processors])}")

        # 执行所有符合条件的处理器
        for processor in available_processors:
            try:
                logger.info(f"执行处理器: {processor.name}")
                result_data = processor.process(email_data, analysis)

                result = ProcessorResult(
                    processor_name=processor.name,
                    success=True,
                    message=f"处理器 {processor.name} 执行成功",
                    data=result_data
                )

                results.append(result)
                logger.info(f"处理器 {processor.name} 执行成功")

            except Exception as e:
                logger.error(f"处理器 {processor.name} 执行失败: {e}")

                result = ProcessorResult(
                    processor_name=processor.name,
                    success=False,
                    message=f"处理器 {processor.name} 执行失败",
                    error=e
                )

                results.append(result)

        # 如果处理成功且是IMAP协议，标记邮件为已读
        if any(r.success for r in results) and self.email_client and self.email_client.config.protocol == "imap":
            if email_id:
                try:
                    success = self.email_client.mark_as_read(email_id, email_folder or "INBOX")
                    if success:
                        logger.info(f"邮件已成功标记为已读: UID {email_id}")
                    else:
                        logger.warning(f"邮件标记为已读失败: UID {email_id}")
                except Exception as e:
                    logger.error(f"标记邮件为已读时发生异常: {e}")
            else:
                logger.warning("无法标记邮件为已读：邮件UID为空")

        return results
    
    def enable_processor(self, name: str):
        """启用处理器"""
        if name in self.processors:
            self.processors[name].enabled = True
            logger.info(f"处理器已启用: {name}")
    
    def disable_processor(self, name: str):
        """禁用处理器"""
        if name in self.processors:
            self.processors[name].enabled = False
            logger.info(f"处理器已禁用: {name}")
    
    def get_processor_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有处理器状态"""
        status = {}
        for name, processor in self.processors.items():
            status[name] = {
                "enabled": processor.enabled,
                "priority": processor.get_priority(),
                "config": processor.config
            }
        return status

    def _get_ai_suggested_processors(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> List[BaseProcessor]:
        """根据AI建议获取处理器列表"""
        suggested_processors = []

        # 按AI建议的优先级排序
        sorted_suggestions = sorted(analysis.processor_suggestions, key=lambda x: x.get('priority', 50))

        for suggestion in sorted_suggestions:
            processor_name = suggestion.get('processor_name', '')
            enabled = suggestion.get('enabled', True)
            reason = suggestion.get('reason', '')

            if not enabled:
                logger.debug(f"AI建议跳过处理器: {processor_name} - 原因: {reason}")
                continue

            if processor_name in self.processors:
                processor = self.processors[processor_name]

                # 检查处理器是否启用
                if not processor.enabled:
                    logger.debug(f"处理器 {processor_name} 已禁用，跳过")
                    continue

                # 检查处理器是否能处理该邮件（双重验证）
                if processor.can_process(email_data, analysis):
                    logger.info(f"AI推荐处理器: {processor_name} - 理由: {reason}")
                    suggested_processors.append(processor)
                else:
                    logger.warning(f"AI推荐的处理器 {processor_name} 无法处理该邮件")
            else:
                logger.warning(f"AI推荐的处理器 {processor_name} 不存在")

        # 确保log_processor总是被包含（如果存在且未被AI推荐）
        if 'log_processor' in self.processors:
            log_processor = self.processors['log_processor']
            if log_processor not in suggested_processors and log_processor.enabled:
                suggested_processors.append(log_processor)
                logger.debug("自动添加log_processor")

        return suggested_processors

    def _get_traditional_processors(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> List[BaseProcessor]:
        """使用传统逻辑获取处理器列表"""
        available_processors = []

        for processor in self.processors.values():
            # 检查处理器是否启用
            if not processor.enabled:
                logger.debug(f"处理器 {processor.name} 已禁用")
                continue

            # 检查处理器是否能处理该邮件
            can_process = processor.can_process(email_data, analysis)
            if not can_process:
                logger.debug(f"处理器 {processor.name} 无法处理该邮件")
                continue

            logger.debug(f"处理器 {processor.name} 可用")
            available_processors.append(processor)

        # 按优先级排序
        available_processors.sort(key=lambda p: p.get_priority())

        return available_processors
