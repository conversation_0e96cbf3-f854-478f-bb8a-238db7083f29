"""
邮件线程上下文引擎
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any
from datetime import datetime
from loguru import logger

from .thread_db import ThreadDB
from .models.thread_models import Thread, Message

class ContextBuilder(ABC):
    """上下文构建策略基类"""
    
    @abstractmethod
    def build_context(self, messages: List[Message]) -> str:
        """构建上下文"""
        pass

class FullHistoryBuilder(ContextBuilder):
    """完整历史构建器"""
    
    def build_context(self, messages: List[Message]) -> str:
        context_parts = []
        for msg in messages:
            context_parts.append(f"时间: {msg.timestamp}\n内容: {msg.content}\n---")
        return "\n".join(context_parts) if context_parts else ""

class SummaryBuilder(ContextBuilder):
    """摘要构建器"""
    
    def __init__(self, max_length: int = 500):
        self.max_length = max_length
    
    def build_context(self, messages: List[Message]) -> str:
        if not messages:
            return ""
        
        # 简单实现：保留最新的几条消息
        recent_messages = messages[-3:]  # 最新3条
        context_parts = []
        for msg in recent_messages:
            content = msg.content
            if len(content) > self.max_length:
                content = content[:self.max_length] + "..."
            context_parts.append(f"摘要: {content}")
        return "\n".join(context_parts)

class KeyPointsBuilder(ContextBuilder):
    """关键点构建器"""
    
    def __init__(self, max_points: int = 5):
        self.max_points = max_points
    
    def build_context(self, messages: List[Message]) -> str:
        if not messages:
            return ""
        
        # 提取元数据中的关键点
        key_points = []
        for msg in messages:
            if 'key_points' in msg.metadata:
                key_points.extend(msg.metadata['key_points'])
        
        # 只保留最重要的几个关键点
        key_points = key_points[:self.max_points]
        return "\n".join(f"- {point}" for point in key_points)

class ContextEngine:
    """上下文引擎"""
    
    def __init__(self, thread_db: ThreadDB):
        """
        初始化上下文引擎
        
        Args:
            thread_db: 线程数据库管理器
        """
        self.thread_db = thread_db
        self.builders: Dict[str, ContextBuilder] = {
            'full': FullHistoryBuilder(),
            'summary': SummaryBuilder(),
            'key_points': KeyPointsBuilder()
        }
    
    def build_context(self, thread_id: str, strategy: str = 'summary') -> str:
        """
        构建线程上下文
        
        Args:
            thread_id: 线程ID
            strategy: 构建策略 ('full', 'summary', 'key_points')
            
        Returns:
            构建的上下文字符串
        """
        try:
            # 获取线程消息
            messages = self.thread_db.get_thread_messages(thread_id)
            
            # 使用指定策略构建上下文
            builder = self.builders.get(strategy)
            if not builder:
                logger.warning(f"未知的构建策略: {strategy}, 使用摘要策略")
                builder = self.builders['summary']
            
            context = builder.build_context(messages)
            logger.info(f"构建上下文成功: thread_id={thread_id}, strategy={strategy}")
            return context
            
        except Exception as e:
            logger.error(f"构建上下文失败: {e}")
            return ""
    
    def add_builder(self, name: str, builder: ContextBuilder) -> None:
        """
        添加自定义构建器
        
        Args:
            name: 构建器名称
            builder: 构建器实例
        """
        self.builders[name] = builder
        logger.info(f"添加构建器: {name}")