"""
邮件客户端模块 - 负责连接和读取邮件
"""

import imaplib
import poplib
import email
import time
import threading
import json
from datetime import datetime
from typing import List, Dict, Any, Union, Optional, cast, Type
from email.message import Message
from email.header import decode_header
from loguru import logger
from .config_manager import EmailConfig


class EmailProcessingError(Exception):
    """邮件处理异常"""
    pass


class EmailStats:
    """邮件处理统计"""
    def __init__(self):
        self.total_processed = 0
        self.successful = 0
        self.failed = 0
        self.start_time = None
        self.end_time = None
        
    def start(self):
        """开始统计"""
        self.start_time = datetime.now()
        
    def complete(self):
        """完成统计"""
        self.end_time = datetime.now()
        
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        return (self.successful / self.total_processed * 100) if self.total_processed > 0 else 0
        
    @property
    def processing_time(self) -> float:
        """计算处理时间(秒)"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0


class EmailMessageWrapper:
    """邮件消息类"""

    def __init__(self, raw_message: Message):
        self.raw_message = raw_message
        self._subject = None
        self._sender = None
        self._recipients = None
        self._body = None
        self._attachments = None
        self.uid: Optional[str] = None
        self.folder: Optional[str] = None
        self.processing_attempts = 0
        self.last_error = None

    @property
    def subject(self) -> str:
        """获取邮件主题"""
        if self._subject is None:
            subject = self.raw_message.get("Subject", "")
            if subject:
                decoded = decode_header(subject)
                self._subject = "".join([
                    text.decode(encoding or 'utf-8') if isinstance(text, bytes) else text
                    for text, encoding in decoded
                ])
            else:
                self._subject = ""
        return self._subject

    @property
    def sender(self) -> str:
        """获取发件人"""
        if self._sender is None:
            sender = self.raw_message.get("From", "")
            if sender:
                decoded = decode_header(sender)
                self._sender = "".join([
                    text.decode(encoding or 'utf-8') if isinstance(text, bytes) else text
                    for text, encoding in decoded
                ])
            else:
                self._sender = ""
        return self._sender

    @property
    def recipients(self) -> List[str]:
        """获取收件人列表"""
        if self._recipients is None:
            to = self.raw_message.get("To", "")
            cc = self.raw_message.get("Cc", "")
            self._recipients = [addr.strip() for addr in (to + "," + cc).split(",") if addr.strip()]
        return self._recipients

    @property
    def body(self) -> str:
        """获取邮件正文"""
        if self._body is None:
            self._body = self._extract_body()
        return self._body

    @property
    def attachments(self) -> List[Dict[str, Any]]:
        """获取附件信息"""
        if self._attachments is None:
            self._attachments = self._extract_attachments()
        return self._attachments

    def _extract_body(self) -> str:
        """提取邮件正文"""
        body = ""
        try:
            if self.raw_message.is_multipart():
                for part in self.raw_message.walk():
                    content_type = part.get_content_type()
                    if content_type == "text/plain":
                        charset = part.get_content_charset() or 'utf-8'
                        payload = part.get_payload(decode=True)
                        if isinstance(payload, bytes):
                            body = payload.decode(charset, errors='ignore')
                        else:
                            body = str(payload)
                        break
                    elif content_type == "text/html" and not body:
                        charset = part.get_content_charset() or 'utf-8'
                        payload = part.get_payload(decode=True)
                        if isinstance(payload, bytes):
                            body = payload.decode(charset, errors='ignore')
                        else:
                            body = str(payload)
            else:
                charset = self.raw_message.get_content_charset() or 'utf-8'
                payload = self.raw_message.get_payload(decode=True)
                if isinstance(payload, bytes):
                    body = payload.decode(charset, errors='ignore')
                else:
                    body = str(payload)
        except Exception as e:
            logger.error(f"提取邮件正文失败: {e}")
            body = str(self.raw_message.get_payload())

        return body

    def _extract_attachments(self) -> List[Dict[str, Any]]:
        """提取附件信息（包含内容）"""
        attachments = []
        if self.raw_message.is_multipart():
            for part in self.raw_message.walk():
                content_disposition = part.get_content_disposition()
                if content_disposition in ["attachment", "inline"] or part.get_filename():
                    filename = part.get_filename()
                    if filename:
                        try:
                            payload = part.get_payload(decode=True)
                            attachments.append({
                                "filename": filename,
                                "content_type": part.get_content_type(),
                                "size": len(payload),
                                "content": payload,
                                "download_url": ""
                            })
                        except Exception as e:
                            logger.error(f"处理附件 {filename} 失败: {e}")
                            
        return attachments

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "subject": self.subject,
            "sender": self.sender,
            "recipients": self.recipients,
            "body": self.body,
            "attachments": self.attachments,
            "date": self.raw_message.get("Date", ""),
            "message_id": self.raw_message.get("Message-ID", ""),
            "uid": self.uid,
            "folder": self.folder,
            "processing_attempts": self.processing_attempts,
            "last_error": str(self.last_error) if self.last_error else None
        }


class EmailClient:
    """邮件客户端"""
    MAX_RETRIES = 3  # 最大重试次数
    RETRY_DELAY = 5  # 重试间隔(秒)
    POLLING_INTERVAL = 300  # 轮询间隔(秒)

    def __init__(self, config: EmailConfig):
        self.config = config
        self._connection: Optional[Union[imaplib.IMAP4_SSL, imaplib.IMAP4, poplib.POP3_SSL, poplib.POP3]] = None
        self._polling_thread: Optional[threading.Thread] = None
        self._stop_polling = threading.Event()
        self._polling_lock = threading.Lock()
        self.stats = EmailStats()
        self._error_log_path = "email_errors.log"

    def _log_error(self, error_info: Dict[str, Any]):
        """记录错误信息到日志文件"""
        try:
            error_info["timestamp"] = datetime.now().isoformat()
            with open(self._error_log_path, "a") as f:
                json.dump(error_info, f)
                f.write("\n")
        except Exception as e:
            logger.error(f"写入错误日志失败: {e}")

    def start_polling(self, folder: str = "INBOX", callback=None):
        """启动邮件轮询"""
        if self._polling_thread and self._polling_thread.is_alive():
            logger.warning("轮询已在运行中")
            return

        def polling_task():
            while not self._stop_polling.is_set():
                try:
                    with self._polling_lock:
                        self.stats.start()
                        emails = self.fetch_emails(folder, unread_only=True)
                        for email in emails:
                            try:
                                if callback:
                                    callback(email)
                                self.stats.successful += 1
                            except Exception as e:
                                self.stats.failed += 1
                                error_info = {
                                    "email_uid": email.uid,
                                    "folder": email.folder,
                                    "error": str(e),
                                    "subject": email.subject
                                }
                                self._log_error(error_info)
                        self.stats.total_processed += len(emails)
                        self.stats.complete()
                        
                        # 输出统计信息
                        logger.info(f"轮询统计: "
                                  f"处理={self.stats.total_processed}, "
                                  f"成功={self.stats.successful}, "
                                  f"失败={self.stats.failed}, "
                                  f"成功率={self.stats.success_rate:.1f}%, "
                                  f"耗时={self.stats.processing_time:.1f}秒")
                                  
                except Exception as e:
                    logger.error(f"轮询任务异常: {e}")
                    time.sleep(self.RETRY_DELAY)  # 发生错误时延迟重试
                    continue
                    
                self._stop_polling.wait(self.POLLING_INTERVAL)

        self._stop_polling.clear()
        self._polling_thread = threading.Thread(target=polling_task)
        self._polling_thread.daemon = True
        self._polling_thread.start()
        logger.info("邮件轮询已启动")

    def stop_polling(self):
        """停止邮件轮询"""
        if self._polling_thread:
            self._stop_polling.set()
            self._polling_thread.join()
            logger.info("邮件轮询已停止")

    def connect(self) -> bool:
        """连接到邮件服务器"""
        try:
            if self.config.protocol == "imap":
                self._connection = self._connect_imap()
            elif self.config.protocol == "pop3":
                self._connection = self._connect_pop3()
            else:
                raise ValueError(f"不支持的协议: {self.config.protocol}")

            logger.info(f"邮件服务器连接成功: {self.config.host}:{self.config.port}")
            return True

        except Exception as e:
            logger.error(f"邮件服务器连接失败: {e}")
            return False

    def _connect_imap(self) -> Union[imaplib.IMAP4_SSL, imaplib.IMAP4]:
        """连接IMAP服务器"""
        if self.config.use_ssl:
            connection = imaplib.IMAP4_SSL(self.config.host, self.config.port)
        else:
            connection = imaplib.IMAP4(self.config.host, self.config.port)

        connection.login(self.config.username, self.config.password)
        return connection

    def _connect_pop3(self) -> Union[poplib.POP3_SSL, poplib.POP3]:
        """连接POP3服务器"""
        if self.config.use_ssl:
            connection = poplib.POP3_SSL(self.config.host, self.config.port)
        else:
            connection = poplib.POP3(self.config.host, self.config.port)

        connection.user(self.config.username)
        connection.pass_(self.config.password)
        return connection

    def fetch_emails(self, folder: str = "INBOX", limit: int = 10, unread_only: bool = True) -> List[EmailMessageWrapper]:
        """获取邮件列表"""
        if not self._connection:
            if not self.connect():
                return []

        try:
            if self.config.protocol == "imap":
                return self._fetch_emails_imap(folder, limit, unread_only)
            elif self.config.protocol == "pop3":
                return self._fetch_emails_pop3(limit)
            else:
                logger.error(f"不支持的协议: {self.config.protocol}")
                return []
        except Exception as e:
            logger.error(f"获取邮件失败: {e}")
            return []

    def _fetch_emails_imap(self, folder: str, limit: int, unread_only: bool) -> List[EmailMessageWrapper]:
        """通过IMAP获取邮件"""
        if self.config.protocol == "imap" and self._connection is not None:
            imap_connection = cast(Union[imaplib.IMAP4_SSL, imaplib.IMAP4], self._connection)
            
            folders = folder.split(',') if ',' in folder else [folder]
            all_emails = []

            for current_folder in folders:
                try:
                    logger.info(f"正在搜索文件夹: {current_folder}")
                    status = imap_connection.select(current_folder)
                    if status[0] != "OK":
                        logger.warning(f"无法选择文件夹 {current_folder}: {status[1]}")
                        continue
                    
                    try:
                        status_all, all_messages = imap_connection.uid('SEARCH', 'ALL')
                        total_count = len(all_messages[0].split()) if status_all == "OK" and all_messages[0] else 0

                        status_unseen, unseen_messages = imap_connection.uid('SEARCH', 'UNSEEN')
                        unseen_count = len(unseen_messages[0].split()) if status_unseen == "OK" and unseen_messages[0] else 0

                        logger.info(f"文件夹 {current_folder} 统计: 总邮件数={total_count}, 未读邮件数={unseen_count}")
                    except Exception as e:
                        logger.warning(f"获取文件夹统计信息失败: {e}")

                    search_criteria = "UNSEEN" if unread_only else "ALL"
                    logger.debug(f"使用搜索条件: {search_criteria}")

                    try:
                        status, messages = imap_connection.uid('SEARCH', search_criteria)
                        logger.debug(f"搜索返回状态: {status}, 消息: {messages}")
                    except imaplib.IMAP4.error as e:
                        logger.error(f"IMAP搜索命令失败: {e}")
                        try:
                            logger.info("尝试使用标准SEARCH命令作为备选方案")
                            status, messages = imap_connection.search(None, search_criteria)
                            logger.debug(f"备选搜索返回状态: {status}, 消息: {messages}")
                            if status == "OK" and messages and messages[0]:
                                msg_nums = messages[0].split()
                                uid_list = []
                                for msg_num in msg_nums:
                                    try:
                                        uid_status, uid_data = imap_connection.fetch(msg_num, '(UID)')
                                        if uid_status == "OK" and uid_data:
                                            uid_response = uid_data[0].decode('utf-8') if isinstance(uid_data[0], bytes) else str(uid_data[0])
                                            import re
                                            uid_match = re.search(r'UID (\d+)', uid_response)
                                            if uid_match:
                                                uid_list.append(uid_match.group(1).encode('utf-8'))
                                    except Exception as uid_error:
                                        logger.warning(f"转换消息序号 {msg_num} 为UID失败: {uid_error}")
                                messages = [b' '.join(uid_list)] if uid_list else [b'']
                        except Exception as fallback_error:
                            logger.error(f"备选搜索方法也失败: {fallback_error}")
                            continue
                    except Exception as e:
                        logger.error(f"搜索邮件时发生未知错误: {e}")
                        continue

                    if status == "OK":
                        if messages and messages[0]:
                            uids = messages[0].split()
                            logger.info(f"找到 {len(uids)} 封符合条件的邮件")
                            uids = uids[-limit:] if len(uids) > limit else uids
                            logger.debug(f"处理邮件UID列表: {[uid.decode() if isinstance(uid, bytes) else str(uid) for uid in uids]}")

                            for uid_item in uids:
                                retry_count = 0
                                while retry_count < self.MAX_RETRIES:
                                    try:
                                        uid_str = uid_item.decode('utf-8') if isinstance(uid_item, bytes) else str(uid_item)
                                        status, msg_data = imap_connection.uid('FETCH', uid_str, '(RFC822)')

                                        if status != "OK" or not msg_data:
                                            raise EmailProcessingError("获取邮件数据失败")

                                        if isinstance(msg_data[0], tuple) and len(msg_data[0]) >= 2:
                                            raw_email_bytes = msg_data[0][1]
                                            if isinstance(raw_email_bytes, bytes):
                                                raw_email = email.message_from_bytes(raw_email_bytes)
                                                email_message = EmailMessageWrapper(raw_email)
                                                email_message.uid = uid_str
                                                email_message.folder = current_folder
                                                all_emails.append(email_message)
                                                logger.debug(f"成功解析邮件: {email_message.subject[:50]}...")
                                                break
                                            
                                    except Exception as e:
                                        retry_count += 1
                                        if retry_count < self.MAX_RETRIES:
                                            logger.warning(f"处理邮件失败，尝试重试 ({retry_count}/{self.MAX_RETRIES}): {e}")
                                            time.sleep(self.RETRY_DELAY)
                                        else:
                                            try:
                                                uid_display = uid_item.decode('utf-8') if isinstance(uid_item, bytes) else str(uid_item)
                                            except Exception:
                                                uid_display = "invalid_uid"
                                            logger.error(f"处理邮件 UID {uid_display} 达到最大重试次数")
                                            self._log_error({
                                                "email_uid": uid_display,
                                                "folder": current_folder,
                                                "error": str(e),
                                                "retry_count": retry_count
                                            })

                        else:
                            logger.info(f"搜索条件 '{search_criteria}' 没有找到任何邮件")
                    else:
                        logger.error(f"搜索失败: {status}")
                except Exception as e:
                    logger.error(f"处理文件夹 {current_folder} 失败: {e}")
            
            return all_emails
        
        logger.warning("IMAP连接不可用")
        return []

    def mark_as_read(self, uid: str, folder: str = "INBOX") -> bool:
        """使用UID标记邮件为已读（仅IMAP协议支持）"""
        if not self._connection or self.config.protocol != "imap":
            logger.warning("无法标记邮件为已读：未连接或协议不支持")
            return False

        if not uid:
            logger.warning("无法标记邮件为已读：UID为空")
            return False

        retry_count = 0
        while retry_count < self.MAX_RETRIES:
            try:
                imap_connection = cast(Union[imaplib.IMAP4_SSL, imaplib.IMAP4], self._connection)

                select_result = imap_connection.select(folder)
                if select_result[0] != 'OK':
                    raise EmailProcessingError(f"无法选择文件夹 {folder}: {select_result[1]}")

                # 开始事务操作
                try:
                    # 检查邮件是否存在
                    check_result = imap_connection.uid('SEARCH', f'UID {uid}')
                    if check_result[0] != 'OK' or not check_result[1][0]:
                        raise EmailProcessingError(f"邮件 UID {uid} 在文件夹 {folder} 中不存在")

                    # 标记邮件为已读
                    result = imap_connection.uid('STORE', uid, '+FLAGS', '\\Seen')

                    if result[0] == 'OK':
                        logger.info(f"邮件标记为已读成功: UID {uid} (文件夹: {folder})")
                        return True
                    else:
                        error_msg = result[1][0].decode('utf-8') if result[1] and result[1][0] else "未知错误"
                        raise EmailProcessingError(f"标记邮件失败: {error_msg}")

                except Exception as e:
                    # 如果发生错误，尝试回滚操作（将邮件标记为未读）
                    try:
                        imap_connection.uid('STORE', uid, '-FLAGS', '\\Seen')
                    except Exception as rollback_error:
                        logger.error(f"回滚邮件状态失败: {rollback_error}")
                    raise e

            except Exception as e:
                retry_count += 1
                if retry_count < self.MAX_RETRIES:
                    logger.warning(f"标记邮件失败，尝试重试 ({retry_count}/{self.MAX_RETRIES}): {e}")
                    time.sleep(self.RETRY_DELAY)
                else:
                    logger.error(f"标记邮件为已读达到最大重试次数: {e}")
                    self._log_error({
                        "operation": "mark_as_read",
                        "email_uid": uid,
                        "folder": folder,
                        "error": str(e),
                        "retry_count": retry_count
                    })
                    return False

        return False

    def _fetch_emails_pop3(self, limit: int) -> List[EmailMessageWrapper]:
        """通过POP3获取邮件"""
        emails = []

        if self.config.protocol == "pop3" and self._connection is not None:
            pop3_connection = cast(Union[poplib.POP3_SSL, poplib.POP3], self._connection)
            
            try:
                num_messages = len(pop3_connection.list()[1])
                start = max(1, num_messages - limit + 1)

                for i in range(start, num_messages + 1):
                    retry_count = 0
                    while retry_count < self.MAX_RETRIES:
                        try:
                            raw_email_lines = pop3_connection.retr(i)[1]
                            raw_email = email.message_from_bytes(b'\n'.join(raw_email_lines))
                            email_msg = EmailMessageWrapper(raw_email)
                            emails.append(email_msg)
                            break
                        except Exception as e:
                            retry_count += 1
                            if retry_count < self.MAX_RETRIES:
                                logger.warning(f"获取邮件 {i} 失败，尝试重试 ({retry_count}/{self.MAX_RETRIES}): {e}")
                                time.sleep(self.RETRY_DELAY)
                            else:
                                logger.error(f"获取邮件 {i} 达到最大重试次数")
                                self._log_error({
                                    "email_number": i,
                                    "error": str(e),
                                    "retry_count": retry_count
                                })
            except Exception as e:
                logger.error(f"POP3获取邮件列表失败: {e}")

        return emails

    def disconnect(self):
        """断开连接"""
        if self._connection:
            try:
                if self.config.protocol == "imap":
                    imap_connection = cast(Union[imaplib.IMAP4_SSL, imaplib.IMAP4], self._connection)
                    imap_connection.logout()
                elif self.config.protocol == "pop3":
                    pop3_connection = cast(Union[poplib.POP3_SSL, poplib.POP3], self._connection)
                    pop3_connection.quit()
                logger.info("邮件服务器连接已断开")
            except Exception as e:
                logger.error(f"断开连接时出错: {e}")
            finally:
                self._connection = None

    def __enter__(self) -> 'EmailClient':
        """上下文管理器进入方法，返回邮件客户端实例"""
        self.connect()
        return self

    def __exit__(self, exc_type: Optional[Type[BaseException]],
                 exc_val: Optional[BaseException],
                 exc_tb: Optional[Any]) -> bool:
        """
        上下文管理器退出方法
        
        Args:
            exc_type: 异常类型
            exc_val: 异常值
            exc_tb: 异常回溯
            
        Returns:
            bool: 是否抑制异常
        """
        self.disconnect()
        return False  # 不抑制异常
