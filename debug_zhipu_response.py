#!/usr/bin/env python3
"""
调试智谱AI搜索响应格式
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.core.config_manager import ConfigManager
from zhipuai import ZhipuAI

async def debug_zhipu_response():
    """调试智谱AI响应格式"""
    logger.info("开始调试智谱AI Web Search响应格式...")
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        # 获取API密钥
        api_key = config.ai.mcp.api_key
        logger.info(f"使用API密钥: {api_key[:10]}...")
        
        # 创建智谱AI客户端
        client = ZhipuAI(api_key=api_key)
        
        # 测试查询
        query = "人工智能最新发展"
        logger.info(f"测试查询: {query}")
        
        # 发送请求
        response = client.chat.completions.create(
            model="glm-4",
            messages=[
                {
                    "role": "user", 
                    "content": f"请搜索关于'{query}'的信息"
                }
            ],
            tools=[
                {
                    "type": "web_search",
                    "web_search": {
                        "search_query": query,
                        "search_result": True,
                    }
                }
            ],
            extra_body={
                "temperature": 0.1,
                "max_tokens": 1000
            }
        )
        
        # 详细打印响应结构
        logger.info("=" * 60)
        logger.info("完整响应对象:")
        logger.info(f"响应类型: {type(response)}")
        logger.info(f"响应属性: {dir(response)}")
        
        if hasattr(response, 'choices'):
            logger.info(f"choices数量: {len(response.choices)}")
            
            for i, choice in enumerate(response.choices):
                logger.info(f"Choice {i}:")
                logger.info(f"  类型: {type(choice)}")
                logger.info(f"  属性: {dir(choice)}")
                
                if hasattr(choice, 'message'):
                    message = choice.message
                    logger.info(f"  Message类型: {type(message)}")
                    logger.info(f"  Message属性: {dir(message)}")
                    
                    if hasattr(message, 'content'):
                        logger.info(f"  Content: {message.content}")
                    
                    if hasattr(message, 'tool_calls'):
                        logger.info(f"  Tool calls: {message.tool_calls}")
                        if message.tool_calls:
                            for j, tool_call in enumerate(message.tool_calls):
                                logger.info(f"    Tool call {j}:")
                                logger.info(f"      类型: {type(tool_call)}")
                                logger.info(f"      属性: {dir(tool_call)}")
                                logger.info(f"      内容: {tool_call}")
        
        # 尝试直接打印整个响应
        logger.info("=" * 60)
        logger.info("尝试直接序列化响应:")
        try:
            import json
            logger.info(json.dumps(response.model_dump(), indent=2, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"无法序列化响应: {e}")
            logger.info(f"响应字符串表示: {str(response)}")
        
        logger.info("=" * 60)
        logger.success("调试完成!")
        
    except Exception as e:
        logger.error(f"调试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行调试
    asyncio.run(debug_zhipu_response())
