# AI Agent 系统配置文件示例
# 展示如何配置升级版的AI回复处理器

# 邮箱配置
email:
  # IMAP/POP3 接收配置
  host: "imap.qq.com"
  port: 993
  username: "<EMAIL>"
  password: "your_password"
  use_ssl: true
  protocol: "imap"

  # SMTP 发送配置
  smtp:
    host: "smtp.qq.com"
    port: 587
    username: "<EMAIL>"
    password: "your_password"
    use_tls: true
    from_name: "AI邮件助手"

# AI分析配置
ai:
  provider: "openai"              # AI提供商: openai 或 anthropic
  api_key: "your_api_key"         # API密钥
  model: "deepseek-chat"          # 模型名称
  base_url: "https://api.deepseek.com/"  # 自定义API地址
  max_tokens: 1000                # 最大token数
  temperature: 0.7                # 温度参数

# 调度器配置
scheduler:
  check_interval: 60              # 检查间隔(秒)
  max_emails_per_check: 50        # 每次检查最大邮件数
  enable_scheduler: true          # 是否启用定时调度

# 日志配置
log:
  level: "INFO"                   # 日志级别
  file_path: "logs/mailer.log"    # 日志文件路径
  max_size: "10 MB"               # 单个日志文件最大大小
  retention: "30 days"            # 日志保留时间

# 处理器配置
processors:
  # 日志处理器
  log_processor:
    enabled: true
    log_file: "logs/email_processing.log"
  
  # 通知处理器
  notification_processor:
    enabled: true
    priority_threshold: "高"
    method: "system"
  
  # 自动回复处理器
  auto_reply_processor:
    enabled: true
    auto_reply_enabled: true
    enabled_categories:
      - "工作"
      - "个人"
      - "技术支持"
    reply_templates:
      "工作": "感谢您的邮件。我已收到您的消息，会尽快回复。"
      "个人": "谢谢您的邮件，我会及时查看并回复。"
      "技术支持": "感谢您的技术咨询，我会仔细查看并尽快回复。"

  # PDF处理器
  pdf_processor:
    enabled: true

  # AI回复处理器 - 升级版AI Agent系统
  ai_reply_processor:
    enabled: true
    ai_prompt: "请根据以下邮件内容生成专业、友好的回复："
    
    # 搜索处理器配置
    search:
      max_results: 5              # 最大搜索结果数
      timeout: 30                 # 搜索超时时间(秒)
      language: "zh"              # 搜索语言
      region: "CN"                # 搜索地区

      # MCP配置（zhipu-web-search-sse）
      mcp:
        enabled: true             # 启用MCP搜索
        server_url: "ws://localhost:8080"  # MCP服务器地址
        api_key: "your_zhipu_api_key"      # API密钥
        timeout: 30               # 连接超时时间
        max_retries: 3            # 最大重试次数

      # 备用搜索配置
      fallback:
        enabled: true             # 启用备用搜索
        timeout: 30               # 搜索超时时间
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

      # 搜索质量评估权重
      quality_weights:
        title_relevance: 0.3      # 标题相关性权重
        snippet_relevance: 0.4    # 摘要相关性权重
        source_authority: 0.2     # 来源权威性权重
        freshness: 0.1            # 时效性权重
    
    # 分析引擎配置
    analysis:
      max_content_length: 2000    # 最大内容长度
      min_confidence_threshold: 0.6  # 最小置信度阈值
      enable_deep_analysis: true  # 启用深度分析
      # 复杂度指标关键词
      complexity_indicators:
        - "详细分析"
        - "深入研究"
        - "全面评估"
        - "系统梳理"
        - "多角度"
        - "综合考虑"
        - "对比分析"
        - "专业建议"
    
    # 任务执行器配置
    task_execution:
      max_execution_time: 300     # 最大执行时间(秒)
      enable_parallel_execution: false  # 启用并行执行
      retry_failed_steps: true    # 重试失败步骤
      max_retries: 2              # 最大重试次数
      # 任务类型优先级
      task_priorities:
        simple_reply: 1
        search_and_reply: 2
        multi_step_analysis: 3
        information_gathering: 4
        problem_solving: 5
        research_task: 6
    
    # 回复生成器配置
    response_generation:
      max_reply_length: 1000      # 最大回复长度
      include_sources: true       # 包含来源信息
      professional_tone: true     # 专业语调
      language: "zh"              # 回复语言
      # AI配置
      ai:
        max_tokens: 1000          # 最大token数
        temperature: 0.7          # 温度参数
        # 回复模板
        templates:
          simple: "感谢您的邮件。{content}"
          search_based: "根据我的搜索和分析，{content}"
          analytical: "基于详细分析，{content}"
          solution: "针对您的问题，{content}"
          research: "基于研究结果，{content}"
    
    # 意图分类器配置
    intent_classification:
      # 搜索触发关键词
      search_triggers:
        - "最新"
        - "新闻"
        - "趋势"
        - "市场"
        - "价格"
        - "比较"
        - "评测"
        - "排名"
        - "推荐"
        - "哪个好"
        - "怎么选"
        - "现状"
        - "发展"
        - "前景"
        - "数据"
        - "统计"
      
      # 意图识别模式
      patterns:
        simple_question:
          - "什么是|是什么|怎么样|如何|为什么"
          - "请问|想知道|了解一下"
          - "\\?|？"
        complex_inquiry:
          - "详细|具体|深入|全面|系统"
          - "分析|比较|评估|研究"
          - "方案|策略|建议|解决方案"
        information_request:
          - "提供|发送|给我|需要"
          - "资料|文档|信息|数据"
          - "最新|更新|状态"
        technical_support:
          - "问题|故障|错误|bug"
          - "不能|无法|失败|异常"
          - "帮助|支持|解决|修复"
        research_request:
          - "调研|研究|分析|报告"
          - "市场|行业|竞争|趋势"
          - "数据|统计|调查"
        greeting:
          - "你好|您好|hello|hi"
          - "早上好|下午好|晚上好"
          - "感谢|谢谢|thank"
        complaint:
          - "投诉|抱怨|不满|问题"
          - "糟糕|差|不好|失望"
          - "退款|赔偿|解决"
        feedback:
          - "反馈|建议|意见|评价"
          - "改进|优化|提升"
          - "体验|感受|看法"
        meeting_request:
          - "会议|开会|讨论|面谈"
          - "时间|安排|预约|约定"
          - "见面|沟通|交流"

# 安全配置
security:
  use_keyring: true               # 是否使用系统密钥环存储密码
  encrypt_config: false          # 是否加密配置文件
  
# 邮件过滤规则
filters:
  # 跳过的发件人
  skip_senders:
    - "noreply@"
    - "no-reply@"
    - "mailer-daemon@"
  
  # 跳过的主题关键词
  skip_subjects:
    - "unsubscribe"
    - "auto-reply"
    - "out of office"
  
  # 只处理的文件夹
  folders:
    - "INBOX"

# AI Agent 系统特定配置
ai_agent:
  # 启用功能开关
  features:
    intent_classification: true   # 意图识别
    web_search: true             # 网络搜索
    multi_step_analysis: true    # 多步骤分析
    task_execution: true         # 任务执行
    response_generation: true    # 回复生成
  
  # 性能配置
  performance:
    cache_enabled: true          # 启用缓存
    cache_ttl: 3600             # 缓存过期时间(秒)
    max_concurrent_tasks: 3      # 最大并发任务数
    memory_limit: "512MB"        # 内存限制
  
  # 监控配置
  monitoring:
    enable_metrics: true         # 启用指标收集
    metrics_interval: 60         # 指标收集间隔(秒)
    log_performance: true        # 记录性能日志
    alert_thresholds:
      response_time: 60          # 响应时间告警阈值(秒)
      error_rate: 0.1           # 错误率告警阈值
      memory_usage: 0.8         # 内存使用率告警阈值
