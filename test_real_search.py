#!/usr/bin/env python3
"""
真实网络搜索功能测试脚本
测试集成了MCP和备用搜索的SearchHandler
"""

import os
import sys
import json
from typing import Dict, Any

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from loguru import logger
from src.processors.ai_agent_components.components.search_handler import SearchHandler
from src.processors.ai_agent_components.models.search_models import SearchType


def test_mock_search():
    """测试模拟搜索功能"""
    logger.info("=== 测试模拟搜索功能 ===")
    
    # 创建搜索处理器（不配置MCP，使用模拟搜索）
    config = {
        "max_results": 3,
        "timeout": 10,
        "language": "zh",
        "region": "CN"
    }
    
    search_handler = SearchHandler(config)
    logger.info(f"搜索模式: {search_handler.search_mode}")
    
    # 执行搜索
    query = "人工智能最新发展"
    search_context = search_handler.search(query, SearchType.GENERAL)
    
    # 显示结果
    logger.info(f"搜索查询: {query}")
    logger.info(f"搜索结果数: {search_context.total_results}")
    logger.info(f"搜索时间: {search_context.search_time:.3f}秒")
    logger.info(f"错误信息: {search_context.error_message}")
    
    if search_context.results:
        logger.info("搜索结果:")
        for i, result in enumerate(search_context.results, 1):
            logger.info(f"{i}. {result.title}")
            logger.info(f"   URL: {result.url}")
            logger.info(f"   摘要: {result.snippet[:100]}...")
            logger.info(f"   相关性: {result.relevance_score:.2f}")
            logger.info(f"   来源: {result.source}")
    
    return search_context


def test_fallback_search():
    """测试备用搜索功能"""
    logger.info("\n=== 测试备用搜索功能 ===")
    
    # 创建搜索处理器（启用备用搜索）
    config = {
        "max_results": 3,
        "timeout": 15,
        "language": "zh",
        "region": "CN",
        "mcp": {
            "enabled": False  # 禁用MCP，强制使用备用搜索
        },
        "fallback": {
            "enabled": True,
            "timeout": 15,
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
    }
    
    search_handler = SearchHandler(config)
    logger.info(f"搜索模式: {search_handler.search_mode}")
    
    # 执行搜索
    query = "Python编程教程"
    search_context = search_handler.search(query, SearchType.TECHNICAL)
    
    # 显示结果
    logger.info(f"搜索查询: {query}")
    logger.info(f"搜索结果数: {search_context.total_results}")
    logger.info(f"搜索时间: {search_context.search_time:.3f}秒")
    logger.info(f"错误信息: {search_context.error_message}")
    
    if search_context.results:
        logger.info("搜索结果:")
        for i, result in enumerate(search_context.results, 1):
            logger.info(f"{i}. {result.title}")
            logger.info(f"   URL: {result.url}")
            logger.info(f"   摘要: {result.snippet[:100]}...")
            logger.info(f"   相关性: {result.relevance_score:.2f}")
            logger.info(f"   来源: {result.source}")
    
    return search_context


def test_mcp_search():
    """测试MCP搜索功能"""
    logger.info("\n=== 测试MCP搜索功能 ===")
    
    # 创建搜索处理器（启用MCP搜索）
    config = {
        "max_results": 3,
        "timeout": 20,
        "language": "zh",
        "region": "CN",
        "mcp": {
            "enabled": True,
            "server_url": "ws://localhost:8080",  # 需要实际的MCP服务器
            "api_key": "test_api_key",
            "timeout": 20,
            "max_retries": 2
        },
        "fallback": {
            "enabled": True,
            "timeout": 15
        }
    }
    
    search_handler = SearchHandler(config)
    logger.info(f"搜索模式: {search_handler.search_mode}")
    
    # 执行搜索
    query = "机器学习算法"
    try:
        search_context = search_handler.search(query, SearchType.ACADEMIC)
        
        # 显示结果
        logger.info(f"搜索查询: {query}")
        logger.info(f"搜索结果数: {search_context.total_results}")
        logger.info(f"搜索时间: {search_context.search_time:.3f}秒")
        logger.info(f"错误信息: {search_context.error_message}")
        
        if search_context.results:
            logger.info("搜索结果:")
            for i, result in enumerate(search_context.results, 1):
                logger.info(f"{i}. {result.title}")
                logger.info(f"   URL: {result.url}")
                logger.info(f"   摘要: {result.snippet[:100]}...")
                logger.info(f"   相关性: {result.relevance_score:.2f}")
                logger.info(f"   来源: {result.source}")
        
        return search_context
        
    except Exception as e:
        logger.error(f"MCP搜索测试失败: {e}")
        logger.info("这是正常的，因为没有实际的MCP服务器运行")
        return None


def test_search_quality():
    """测试搜索质量评估"""
    logger.info("\n=== 测试搜索质量评估 ===")
    
    config = {
        "max_results": 5,
        "quality_weights": {
            "title_relevance": 0.4,
            "snippet_relevance": 0.3,
            "source_authority": 0.2,
            "freshness": 0.1
        }
    }
    
    search_handler = SearchHandler(config)
    
    # 测试多个查询
    queries = [
        "深度学习",
        "区块链技术",
        "云计算服务"
    ]
    
    for query in queries:
        logger.info(f"\n测试查询: {query}")
        search_context = search_handler.search(query)
        
        if search_context.results:
            # 显示相关性评分
            for i, result in enumerate(search_context.results, 1):
                logger.info(f"{i}. {result.title} (评分: {result.relevance_score:.3f})")
        
        # 显示摘要
        summary = search_context.get_summary()
        logger.info(f"搜索摘要:\n{summary[:200]}...")


def test_multiple_search_types():
    """测试不同搜索类型"""
    logger.info("\n=== 测试不同搜索类型 ===")
    
    search_handler = SearchHandler()
    
    test_cases = [
        ("通用搜索", "最新科技新闻", SearchType.GENERAL),
        ("技术搜索", "Python异步编程", SearchType.TECHNICAL),
        ("学术搜索", "量子计算研究", SearchType.ACADEMIC),
        ("新闻搜索", "今日头条新闻", SearchType.NEWS),
        ("产品搜索", "智能手机推荐", SearchType.PRODUCT)
    ]
    
    for test_name, query, search_type in test_cases:
        logger.info(f"\n{test_name}: {query}")
        search_context = search_handler.search(query, search_type, max_results=2)
        
        logger.info(f"结果数: {search_context.total_results}")
        if search_context.results:
            for result in search_context.results:
                logger.info(f"- {result.title} (评分: {result.relevance_score:.2f})")


def main():
    """主测试函数"""
    logger.info("开始测试真实网络搜索功能")
    
    # 测试模拟搜索
    mock_result = test_mock_search()
    
    # 测试备用搜索（需要网络连接）
    try:
        fallback_result = test_fallback_search()
    except Exception as e:
        logger.error(f"备用搜索测试失败: {e}")
        fallback_result = None
    
    # 测试MCP搜索（需要MCP服务器）
    mcp_result = test_mcp_search()
    
    # 测试搜索质量
    test_search_quality()
    
    # 测试不同搜索类型
    test_multiple_search_types()
    
    # 总结
    logger.info("\n=== 测试总结 ===")
    logger.info(f"模拟搜索: {'成功' if mock_result and mock_result.results else '失败'}")
    logger.info(f"备用搜索: {'成功' if fallback_result and fallback_result.results else '失败/跳过'}")
    logger.info(f"MCP搜索: {'成功' if mcp_result and mcp_result.results else '失败/跳过'}")
    
    logger.info("\n搜索功能测试完成！")
    logger.info("注意：")
    logger.info("1. MCP搜索需要运行zhipu-web-search-sse MCP服务器")
    logger.info("2. 备用搜索需要网络连接")
    logger.info("3. 模拟搜索始终可用，用作最后的备用方案")


if __name__ == "__main__":
    main()
